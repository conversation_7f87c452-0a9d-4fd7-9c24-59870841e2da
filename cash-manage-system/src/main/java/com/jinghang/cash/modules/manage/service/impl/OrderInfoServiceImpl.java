package com.jinghang.cash.modules.manage.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jinghang.cash.enums.CallState;
import com.jinghang.cash.enums.LoanState;
import com.jinghang.cash.enums.ProcessState;
import com.jinghang.cash.enums.RepayState;
import com.jinghang.cash.enums.ResultCode;
import com.jinghang.cash.enums.RightsState;
import com.jinghang.cash.enums.WhetherState;
import com.jinghang.cash.mapper.AgreementSignRelationMapper;
import com.jinghang.cash.mapper.BankRepayRecordMapper;
import com.jinghang.cash.mapper.CreditMapper;
import com.jinghang.cash.mapper.CustomRepayRecordMapper;
import com.jinghang.cash.mapper.LoanMapper;
import com.jinghang.cash.mapper.OrderEventRecordMapper;
import com.jinghang.cash.mapper.OrderMapper;
import com.jinghang.cash.mapper.RepayPlanMapper;
import com.jinghang.cash.mapper.RightsBasePackageMapper;
import com.jinghang.cash.mapper.RightsRepayRecordMapper;
import com.jinghang.cash.mapper.UserFaceMapper;
import com.jinghang.cash.mapper.UserFileMapper;
import com.jinghang.cash.mapper.UserInfoMapper;
import com.jinghang.cash.mapper.UserOcrMapper;
import com.jinghang.cash.mapper.UserRiskRecordMapper;
import com.jinghang.cash.modules.manage.BizException;
import com.jinghang.cash.modules.manage.remote.BusinessFileService;
import com.jinghang.cash.modules.manage.remote.BusinessOrderService;
import com.jinghang.cash.modules.manage.remote.FinVoucherFileService;
import com.jinghang.cash.modules.manage.service.FileService;
import com.jinghang.cash.modules.manage.service.OrderInfoService;
import com.jinghang.cash.modules.manage.vo.CallRecordVo;
import com.jinghang.cash.modules.manage.vo.CallUserVo;
import com.jinghang.cash.modules.manage.vo.OrderInfoVo;
import com.jinghang.cash.modules.manage.vo.req.AgreementFileReq;
import com.jinghang.cash.modules.manage.vo.req.CallRecordSaveReq;
import com.jinghang.cash.modules.manage.vo.req.OrderInfoReq;
import com.jinghang.cash.modules.manage.vo.req.OrderListReq;
import com.jinghang.cash.modules.manage.vo.req.UserOrderReq;
import com.jinghang.cash.modules.manage.vo.req.ppd.CallRecordQueryReq;
import com.jinghang.cash.modules.manage.vo.rsp.AgreementFileRsp;
import com.jinghang.cash.modules.manage.vo.rsp.OrderListRsp;
import com.jinghang.cash.modules.manage.vo.rsp.RepayPlanRsp;
import com.jinghang.cash.modules.manage.vo.rsp.RepayPlanVo;
import com.jinghang.cash.modules.manage.vo.rsp.StageRsp;
import com.jinghang.cash.modules.manage.vo.rsp.UserFaceRsp;
import com.jinghang.cash.modules.manage.vo.rsp.UserInfoRsp;
import com.jinghang.cash.modules.system.domain.CallRecord;
import com.jinghang.cash.modules.system.domain.CallUser;
import com.jinghang.cash.modules.system.domain.User;
import com.jinghang.cash.modules.system.repository.CallRecordRepository;
import com.jinghang.cash.modules.system.repository.CallUserRepository;
import com.jinghang.cash.modules.system.repository.UserRepository;
import com.jinghang.cash.pojo.AgreementSignRelation;
import com.jinghang.cash.pojo.BankRepayRecord;
import com.jinghang.cash.pojo.Credit;
import com.jinghang.cash.pojo.CustomRepayRecord;
import com.jinghang.cash.pojo.Loan;
import com.jinghang.cash.pojo.Order;
import com.jinghang.cash.pojo.OrderEventRecord;
import com.jinghang.cash.pojo.RepayPlan;
import com.jinghang.cash.pojo.RightsBasePackage;
import com.jinghang.cash.pojo.RightsOrder;
import com.jinghang.cash.pojo.RightsRepayRecord;
import com.jinghang.cash.pojo.UserBankCard;
import com.jinghang.cash.pojo.UserFace;
import com.jinghang.cash.pojo.UserFile;
import com.jinghang.cash.pojo.UserInfo;
import com.jinghang.cash.pojo.UserOcr;
import com.jinghang.cash.pojo.UserRiskRecord;
import com.jinghang.cash.service.RightsOrderService;
import com.jinghang.cash.service.UserBankCardService;
import com.jinghang.cash.utils.SecurityUtils;
import com.jinghang.common.util.JsonUtil;
import com.jinghang.common.util.StringUtil;
import com.jinghang.ppd.api.dto.ResetPasswordReq;
import com.jinghang.ppd.api.dto.RestResult;
import com.jinghang.ppd.api.dto.file.FileDownloadReqDTO;
import com.jinghang.ppd.api.dto.file.FileDownloadRespDTO;
import com.jinghang.ppd.api.dto.order.OrderParamDto;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class OrderInfoServiceImpl implements OrderInfoService {

    private static final Logger logger = LoggerFactory.getLogger(OrderInfoServiceImpl.class);

    private static final Long EXPIRATION_DAY = 7L;

    @Autowired
    private CreditMapper creditMapper;
    @Autowired
    private LoanMapper loanMapper;
    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private UserInfoMapper userInfoMapper;
    @Autowired
    private UserFaceMapper userFaceMapper;
    @Autowired
    private RepayPlanMapper repayPlanMapper;
    @Autowired
    private CustomRepayRecordMapper customRepayRecordMapper;
    @Autowired
    private BankRepayRecordMapper bankRepayRecordMapper;
    @Autowired
    private UserRiskRecordMapper userRiskRecordMapper;
    @Autowired
    private FileService fileService;
    @Autowired
    private RightsBasePackageMapper rightsBasePackageMapper;
    @Autowired
    private UserFileMapper userFileMapper;
    @Autowired
    private UserOcrMapper userOcrMapper;
    @Autowired
    private FinVoucherFileService finVoucherFileService;
    @Autowired
    private AgreementSignRelationMapper agreementSignRelationMapper;
    @Autowired
    private RightsRepayRecordMapper rightsRepayRecordMapper;

    @Autowired
    private BusinessOrderService businessOrderService;


    @Autowired
    private UserBankCardService userBankCardService;
    @Autowired
    private OrderEventRecordMapper orderEventRecordMapper;
    @Autowired
    private BusinessFileService businessFileService;
    @Autowired
    private RightsOrderService rightsOrderService;
    @Autowired
    private UserRepository userRepository;
    @Autowired
    private CallUserRepository callUserRepository;
    @Autowired
    private CallRecordRepository callRecordRepository;

    @Override
    public FileDownloadRespDTO downloadVoucherFile(FileDownloadReqDTO req) {
        com.jinghang.ppd.api.dto.RestResult<FileDownloadRespDTO> result = businessFileService.download(req);
        if (result.isSuccess()) {
            return result.getData();
        } else {
            throw new BizException(result.getMsg(), ResultCode.BIZ_ERROR);
        }
    }

    @Override
    public PageInfo<OrderInfoVo>    queryOrderInfo(OrderInfoReq orderInfoReq) {
        logger.info("订单详情查询开始请求参数:{}", JsonUtil.toJsonString(orderInfoReq));
        PageHelper.startPage(orderInfoReq.getPageNum(), orderInfoReq.getPageSize());
        Page<OrderInfoVo> orderInfoVos = orderMapper.queryOrderInfo(orderInfoReq);
        for (OrderInfoVo orderVo : orderInfoVos) {
            orderVo.setOrderId(orderVo.getOrderId());
            //查询该订单放款成功金额
            QueryWrapper<Loan> qw = new QueryWrapper<>();
            qw.lambda().eq(Loan::getOrderId, orderVo.getOrderId()).eq(Loan::getUserId, orderVo.getUserId()).eq(Loan::getLoanState, "SUCCEED");
            Loan loan = loanMapper.selectOne(qw);
            logger.info("订单对应放款数据:{}", JsonUtil.toJsonString(loan));
            if (ObjectUtils.isNotEmpty(loan)) {
                //放款金额
                orderVo.setLoanAmount(loan.getAmount());
            }
            //查询权益包
            if (StringUtils.isNotBlank(orderVo.getRightsPackageId())) {
                RightsBasePackage rightsBasePackage = rightsBasePackageMapper.selectById(orderVo.getRightsPackageId());
                if (ObjectUtils.isNotEmpty(rightsBasePackage)) {
                    orderVo.setRightsSupplier(rightsBasePackage.getChannel());
                }
                RightsOrder rightsOrder = rightsOrderService.getSuccessOrderById(orderVo.getOrderId(), "SUCCEED");
                if (ObjectUtils.isNotEmpty(rightsOrder)) {
                    orderVo.setRightsPayChannel(rightsOrder.getPayChannel());
                }
            }

            //if (ObjectUtils.isNotEmpty(rightsBasePackage)) {
            //    orderVo.setRightsAmount(rightsBasePackage.getSellingPrice()); //权益金额
            //    orderVo.setRightsStatus("已订购");
            //} else {
            //    orderVo.setRightsAmount(new BigDecimal(0)); //权益金额
            //    orderVo.setRightsStatus("未订购");
            //}
            //String rightsStatus = StringUtil.isNotBlank(orderVo.getRightsPackageId()) ? "已订购" : "未订购";
            //新增权益订单状态未订购、已支付、未支付、已订购
            /** halo
             * String rightsStatus = getRightsStatus(orderVo, loan);
             * orderVo.setRightsStatus(rightsStatus);
             */
            //获取用户性别
            if (null != orderVo.getUserId()) {
                //获取用户性别
                String genderName = getGenderName(orderVo.getUserId());
                orderVo.setGender(genderName);
            }
//            OrderCouponRecord orderCouponRecord = orderCouponRecordMapper.getByOrderIdAndCouponType(orderVo.getOrderId(), CouponType.RIGHTS_DISCOUNT);
//            orderVo.setRightsAmount(orderCouponRecord != null ? orderCouponRecord.getOriginAmount() : orderVo.getRightsActualAmount());

        }
        //分页后数据
        PageInfo<OrderInfoVo> orderInfoVoPageInfo = new PageInfo<>(orderInfoVos);
        logger.info("分页查询订单信息响应:{}", JsonUtil.toJsonString(orderInfoVoPageInfo));
        return orderInfoVoPageInfo;
    }


    /**
     * 获取订单的权益状态
     *
     * @param orderVo 订单信息
     * @param loan    放款信息
     * @return 返回权益状态描述
     */
    private String getRightsStatus(OrderInfoVo orderVo, Loan loan) {
        // 从数据库中获取权益偿还记录
        RightsRepayRecord rightsRepay = null;
        if (ObjectUtils.isNotEmpty(loan)) {
            rightsRepay = rightsRepayRecordMapper.findByLongId(loan.getId());
        }

        // 检查订单是否已提交和权益是否已标记
        boolean isOrderSubmitted = orderVo.getOrderSubmitState().equals(WhetherState.Y.name());
        boolean isRightsMarked = orderVo.getRightsMarking().equals(WhetherState.Y.name());

        // 如果订单已提交
        if (isOrderSubmitted) {
            // 如果权益已标记，获取标记订单的权益状态
            if (isRightsMarked) {
                return getRightsStatusForMarkedOrder(rightsRepay);
            } else {
                // 否则，返回未下单的状态描述
                return RightsState.NOTORDERED.getDesc();
            }
        }

        // 如果订单未提交，返回null
        return null;
    }

    /**
     * 获取标记订单的权益状态
     *
     * @param rightsRepay 权益偿还记录
     * @return 返回权益状态
     */
    private String getRightsStatusForMarkedOrder(RightsRepayRecord rightsRepay) {
        // 如果权益偿还记录存在且偿还状态不为空
        if (ObjectUtils.isNotEmpty(rightsRepay) && StringUtil.isNotBlank(rightsRepay.getRepayState())) {
            // 如果偿还状态为成功，返回已支付的状态描述
            if (rightsRepay.getRepayState().equals(ProcessState.SUCCEED.name())) {
                return RightsState.PAID.getDesc();
            } else if (rightsRepay.getRepayState().equals(ProcessState.FAILED.name())) {
                // 如果偿还状态为失败，返回未支付的状态描述
                return RightsState.NOTPAID.getDesc();
            }
        }
        // 如果权益偿还记录不存在或偿还状态为空，返回已下单的状态描述
        return RightsState.ORDERED.getDesc();
    }

    @Override
    public UserInfoRsp queryUserInfo(UserOrderReq userOrderReq) {
        logger.info("用户信息查询开始请求参数:{}", JsonUtil.toJsonString(userOrderReq));
        UserInfoRsp userInfo = userInfoMapper.queryUserInfo(userOrderReq);
        //获取用户性别
        if (null != userInfo) {
            String genderName = getGenderName(userInfo.getUserId());
            userInfo.setGender(genderName);
            if (userOrderReq.getOrderId() != null) {
                Order order = orderMapper.selectById(userOrderReq.getOrderId());
                UserBankCard userBankCard = userBankCardService.getById(order.getLoanCardId());
                if (userBankCard != null) {
                    userInfo.setBankCardNo(userBankCard.getCardNo());
                    userInfo.setBankName(userBankCard.getBankName());
                }
            }
        }
        logger.info("用户信息:{}", JsonUtil.toJsonString(userInfo));
        return userInfo;
    }

    @Override
    public UserFaceRsp queryUserFileInfo(String userId) {
        logger.info("用户人脸信息查询开始请求参数:{}", userId);
        QueryWrapper<UserFace> qw = new QueryWrapper<>();
        qw.lambda().eq(UserFace::getUserId, userId);
        UserFace userFace = userFaceMapper.selectOne(qw);
        logger.info("用户人脸信息:{}", JsonUtil.toJsonString(userFace));
        UserFaceRsp userFaceRsp = new UserFaceRsp();
        if (ObjectUtils.isNotEmpty(userFace)) {
            String ossUrl = fileService.getOssUrl(userFace.getOssBucket(), userFace.getOssKey());
            userFaceRsp.setUrl(ossUrl);
            userFaceRsp.setUserId(userId);
            userFaceRsp.setExpirationDate(LocalDate.now().plusDays(EXPIRATION_DAY).toString());
        }else {
            // TODO 给个默认的用于演示  可以删除掉
            userFaceRsp.setUrl("http://gips3.baidu.com/it/u=3886271102,3123389489&fm=3028&app=3028&f=JPEG&fmt=auto?w=1280&h=960");
            userFaceRsp.setUserId("UI250212152129481957381558730487");
            userFaceRsp.setExpirationDate("2023-01-01");
            return userFaceRsp;
        }
        return userFaceRsp;
    }

    /**
     * 还款计划查询
     *
     * @param userOrderReq
     * @return
     */
    @Override
    public RepayPlanVo queryRepayPlan(UserOrderReq userOrderReq) {
        String orderId = userOrderReq.getOrderId();
        logger.info("查询还款计划开始请求参数:{}", orderId);
        Order order = orderMapper.selectById(orderId);
        logger.info("还款计划订单信息:{}", JsonUtil.toJsonString(order));
        RepayPlanVo repayPlanVo = new RepayPlanVo();
        if (ObjectUtils.isEmpty(order)) {
            logger.info("未查询到订单信息:{}", JsonUtil.toJsonString(order));
            return repayPlanVo;
        }
        List<RepayPlanRsp> planRspList = new ArrayList<>();

        //根据订单信息查询放款数据
        Loan loan = loanMapper.findByOrderId(orderId);
        if (ObjectUtils.isEmpty(loan)) {
            //未查询到还款计划
            repayPlanVo.setOrderId(orderId);
            repayPlanVo.setUserId(order.getUserId());
            repayPlanVo.setRepayPlanRsps(planRspList);
            return repayPlanVo;
        }

        //根据放款id查询还款计划
        String loanId = loan.getId();
        QueryWrapper<RepayPlan> qw = new QueryWrapper<>();
        qw.lambda().eq(RepayPlan::getLoanId, loanId).orderBy(true, true, RepayPlan::getPeriod);
        List<RepayPlan> repayPlanList = repayPlanMapper.selectList(qw);
        logger.info("还款计划信息:{}", JsonUtil.toJsonString(repayPlanList));

        if (repayPlanList.isEmpty()) {
            //未查询到还款计划
            repayPlanVo.setOrderId(orderId);
            repayPlanVo.setUserId(order.getUserId());
            repayPlanVo.setRepayPlanRsps(planRspList);
            return repayPlanVo;
        }
        for (RepayPlan repayPlan : repayPlanList) {
            RepayPlanRsp repayPlanRsp = new RepayPlanRsp();
            repayPlanRsp.setPeriod(repayPlan.getPeriod()); //期数
            repayPlanRsp.setPlanRepayDate(repayPlan.getPlanRepayDate()); //应还时间
            repayPlanRsp.setAmount(repayPlan.getAmount()); //应还总额
            repayPlanRsp.setPrincipalAmt(repayPlan.getPrincipalAmt()); //应还本金
            repayPlanRsp.setInterestAmt(repayPlan.getInterestAmt()); //应还利息
            repayPlanRsp.setGuaranteeAmt(repayPlan.getGuaranteeAmt()); //应还担保费
            repayPlanRsp.setConsultFee(repayPlan.getConsultFee()); //应还咨询费
            repayPlanRsp.setPenaltyAmt(repayPlan.getPenaltyAmt()); //应还罚息 应还平台罚息
            //根据对客对资记录
            BankRepayRecord bankRepayRecord = getBankRepayRecord(repayPlan);
            if (ObjectUtils.isNotEmpty(bankRepayRecord)) {
                repayPlanRsp.setPenaltyBankAmt(bankRepayRecord.getPenalty()); //应还资方罚息
                repayPlanRsp.setPenaltyTotalAmt(bankRepayRecord.getPenalty().add(repayPlan.getPenaltyAmt()));// 应还总罚息
                repayPlanRsp.setRepayMode(bankRepayRecord.getRepayMode()); //还款方式
            }

            //当期状态
            int i = DateUtil.compare(repayPlan.getPlanRepayDate(), new Date());
            if (i > 0) {
                repayPlanRsp.setNowRepayState("未到期");
                repayPlanRsp.setOverdueDay("0");
            } else {
                repayPlanRsp.setNowRepayState("到期");
                //当期逾期状态
                boolean after;
                long days;
                if (null == repayPlan.getActRepayTime()) {
                    Date date = new Date();
                    after = date.after(repayPlan.getPlanRepayDate());
                    days = DateUtil.betweenDay(date, repayPlan.getPlanRepayDate(), true);
                } else {
                    after = repayPlan.getActRepayTime().after(repayPlan.getPlanRepayDate());
                    days = DateUtil.betweenDay(repayPlan.getActRepayTime(), repayPlan.getPlanRepayDate(), true);
                }
                if (after) {
                    if (days > 0) {
                        repayPlanRsp.setOverdueDay(days + ""); //逾期天数
                    } else {
                        repayPlanRsp.setOverdueDay("0");
                    }
                } else {
                    repayPlanRsp.setOverdueDay("0");
                }
            }

            if ("REPAID".equals(repayPlan.getCustRepayState())) {
                repayPlanRsp.setCustRepayState("已还"); //还款状态
            } else {
                repayPlanRsp.setCustRepayState("未还"); //还款状态
            }
            repayPlanRsp.setActRepayTime(repayPlan.getActRepayTime());
            repayPlanRsp.setActAmount(repayPlan.getActAmount());
            repayPlanRsp.setReduceAmount(new BigDecimal(0)); //减免金额
            repayPlanRsp.setOverflowAmount(new BigDecimal(0)); //溢出金额
            planRspList.add(repayPlanRsp);
        }

        repayPlanVo.setOrderId(orderId);
        repayPlanVo.setUserId(order.getUserId());
        repayPlanVo.setLoanId(repayPlanList.get(0).getLoanId());
        repayPlanVo.setRepayPlanRsps(planRspList);
        return repayPlanVo;
    }

    /**
     * 查看进度
     *
     * @param userOrderReq
     * @return
     */
    @Override
    public List<StageRsp> queryStage(UserOrderReq userOrderReq) {
        logger.info("查看进度请求orderId:{}", userOrderReq.getOrderId());
        //用户信息同步查询
        Order order = orderMapper.selectById(userOrderReq.getOrderId());
        List<StageRsp> stageRspList = new ArrayList<>();
        if (ObjectUtils.isEmpty(order)) {
            logger.info("未查询到订单信息:{}", JsonUtil.toJsonString(order));
            return stageRspList;
        }
        UserInfo userInfo = userInfoMapper.selectById(order.getUserId());
        //查询资方放款结果
        QueryWrapper<Loan> qw = new QueryWrapper<>();
        qw.lambda().eq(Loan::getUserId, order.getUserId()).eq(Loan::getOrderId, userOrderReq.getOrderId()).eq(Loan::getLoanState, "SUCCEED");
        Loan loan = loanMapper.selectOne(qw);
        if (null != loan) {
            StageRsp stageRsp6 = new StageRsp();
            stageRsp6.setStageNo(6);
            stageRsp6.setStageState("已完成");
            stageRsp6.setStageStateName("资方放款");
            stageRspList.add(stageRsp6);
            StageRsp stageRsp5 = new StageRsp();
            stageRsp5.setStageNo(5);
            stageRsp5.setStageState("已完成");
            stageRsp5.setStageStateName("用户要款");
            stageRspList.add(stageRsp5);
            StageRsp stageRsp4 = new StageRsp();
            stageRsp4.setStageNo(4);
            stageRsp4.setStageState("已完成");
            stageRsp4.setStageStateName("资方授信");
            stageRspList.add(stageRsp4);
            StageRsp stageRsp3 = new StageRsp();
            stageRsp3.setStageNo(3);
            stageRsp3.setStageState("已完成");
            stageRsp3.setStageStateName("借款申请");
            stageRspList.add(stageRsp3);
            StageRsp stageRsp2 = new StageRsp();
            stageRsp2.setStageNo(2);
            stageRsp2.setStageState("已完成");
            stageRsp2.setStageStateName("额度审批");
            stageRspList.add(stageRsp2);
            StageRsp stageRsp1 = new StageRsp();
            stageRsp1.setStageNo(1);
            stageRsp1.setStageState("已完成");
            stageRsp1.setStageStateName("用户信息同步");
            stageRspList.add(stageRsp1);
        } else {
            QueryWrapper<Loan> qwp = new QueryWrapper<>();
            qwp.lambda().eq(Loan::getUserId, order.getUserId()).eq(Loan::getOrderId, userOrderReq.getOrderId()).eq(Loan::getLoanState, "PROCESSING");
            Loan loanP = loanMapper.selectOne(qwp);
            if (null != loanP) {
                StageRsp stageRsp6 = new StageRsp();
                stageRsp6.setStageNo(6);
                stageRsp6.setStageState("未完成");
                stageRsp6.setStageStateName("资方放款");
                stageRspList.add(stageRsp6);
                StageRsp stageRsp5 = new StageRsp();
                stageRsp5.setStageNo(5);
                stageRsp5.setStageState("已完成");
                stageRsp5.setStageState("用户要款");
                stageRspList.add(stageRsp5);
                StageRsp stageRsp4 = new StageRsp();
                stageRsp4.setStageNo(4);
                stageRsp4.setStageState("已完成");
                stageRsp4.setStageStateName("资方授信");
                stageRspList.add(stageRsp4);
                StageRsp stageRsp3 = new StageRsp();
                stageRsp3.setStageNo(3);
                stageRsp3.setStageState("已完成");
                stageRsp3.setStageStateName("借款申请");
                stageRspList.add(stageRsp3);
                StageRsp stageRsp2 = new StageRsp();
                stageRsp2.setStageNo(2);
                stageRsp2.setStageState("已完成");
                stageRsp2.setStageStateName("额度审批");
                stageRspList.add(stageRsp2);
                StageRsp stageRsp1 = new StageRsp();
                stageRsp1.setStageNo(1);
                stageRsp1.setStageState("已完成");
                stageRsp1.setStageStateName("用户信息同步");
                stageRspList.add(stageRsp1);
            } else {
                //授信阶段
                QueryWrapper<Credit> qwCredit = new QueryWrapper<>();
                qwCredit.lambda().eq(Credit::getOrderId, userOrderReq.getOrderId()).eq(Credit::getState, "SUCCEED");
                Credit credit = creditMapper.selectOne(qwCredit);
                if (null != credit) {
                    StageRsp stageRsp6 = new StageRsp();
                    stageRsp6.setStageNo(6);
                    stageRsp6.setStageState("未完成");
                    stageRsp6.setStageStateName("资方放款");
                    stageRspList.add(stageRsp6);
                    StageRsp stageRsp5 = new StageRsp();
                    stageRsp5.setStageNo(5);
                    stageRsp5.setStageState("未完成");
                    stageRsp5.setStageStateName("用户要款");
                    stageRspList.add(stageRsp5);
                    StageRsp stageRsp4 = new StageRsp();
                    stageRsp4.setStageNo(4);
                    stageRsp4.setStageState("已完成");
                    stageRsp4.setStageStateName("资方授信");
                    stageRspList.add(stageRsp4);
                    StageRsp stageRsp3 = new StageRsp();
                    stageRsp3.setStageNo(3);
                    stageRsp3.setStageState("已完成");
                    stageRsp3.setStageStateName("借款申请");
                    stageRspList.add(stageRsp3);
                    StageRsp stageRsp2 = new StageRsp();
                    stageRsp2.setStageNo(2);
                    stageRsp2.setStageState("已完成");
                    stageRsp2.setStageStateName("额度审批");
                    stageRspList.add(stageRsp2);
                    StageRsp stageRsp1 = new StageRsp();
                    stageRsp1.setStageNo(1);
                    stageRsp1.setStageState("已完成");
                    stageRsp1.setStageStateName("用户信息同步");
                    stageRspList.add(stageRsp1);
                } else {
                    //判断借款申请
                    if (order.getOrderState().equals("LOAN_PASS")) {
                        StageRsp stageRsp6 = new StageRsp();
                        stageRsp6.setStageNo(6);
                        stageRsp6.setStageState("未完成");
                        stageRsp6.setStageStateName("资方放款");
                        stageRspList.add(stageRsp6);
                        StageRsp stageRsp5 = new StageRsp();
                        stageRsp5.setStageNo(5);
                        stageRsp5.setStageState("未完成");
                        stageRsp5.setStageStateName("用户要款");
                        stageRspList.add(stageRsp5);
                        StageRsp stageRsp4 = new StageRsp();
                        stageRsp4.setStageNo(4);
                        stageRsp4.setStageState("未完成");
                        stageRsp4.setStageStateName("资方授信");
                        stageRspList.add(stageRsp4);
                        StageRsp stageRsp3 = new StageRsp();
                        stageRsp3.setStageNo(3);
                        stageRsp3.setStageState("已完成");
                        stageRsp3.setStageStateName("借款申请");
                        stageRspList.add(stageRsp3);
                        StageRsp stageRsp2 = new StageRsp();
                        stageRsp2.setStageNo(2);
                        stageRsp2.setStageState("已完成");
                        stageRsp2.setStageStateName("额度审批");
                        stageRspList.add(stageRsp2);
                        StageRsp stageRsp1 = new StageRsp();
                        stageRsp1.setStageNo(1);
                        stageRsp1.setStageState("已完成");
                        stageRsp1.setStageStateName("用户信息同步");
                        stageRspList.add(stageRsp1);
                    } else {
                        QueryWrapper<UserRiskRecord> urqw = new QueryWrapper<>();
                        urqw.lambda().eq(UserRiskRecord::getUserId, userInfo.getId()).eq(UserRiskRecord::getApproveResult, "PASS");
                        UserRiskRecord userRiskRecord = userRiskRecordMapper.selectOne(urqw);
                        if (null != userRiskRecord) {
                            StageRsp stageRsp6 = new StageRsp();
                            stageRsp6.setStageNo(6);
                            stageRsp6.setStageState("未完成");
                            stageRsp6.setStageStateName("资方放款");
                            stageRspList.add(stageRsp6);
                            StageRsp stageRsp5 = new StageRsp();
                            stageRsp5.setStageNo(5);
                            stageRsp5.setStageState("未完成");
                            stageRsp5.setStageStateName("用户要款");
                            stageRspList.add(stageRsp5);
                            StageRsp stageRsp4 = new StageRsp();
                            stageRsp4.setStageNo(4);
                            stageRsp4.setStageState("未完成");
                            stageRsp4.setStageStateName("资方授信");
                            stageRspList.add(stageRsp4);
                            StageRsp stageRsp3 = new StageRsp();
                            stageRsp3.setStageNo(3);
                            stageRsp3.setStageState("已完成");
                            stageRsp3.setStageStateName("借款申请");
                            stageRspList.add(stageRsp3);
                            StageRsp stageRsp2 = new StageRsp();
                            stageRsp2.setStageNo(2);
                            stageRsp2.setStageState("已完成");
                            stageRsp2.setStageStateName("额度审批");
                            stageRspList.add(stageRsp2);
                            StageRsp stageRsp1 = new StageRsp();
                            stageRsp1.setStageNo(1);
                            stageRsp1.setStageState("已完成");
                            stageRsp1.setStageStateName("用户信息同步");
                            stageRspList.add(stageRsp1);
                        } else {
                            StageRsp stageRsp6 = new StageRsp();
                            stageRsp6.setStageNo(6);
                            stageRsp6.setStageState("未完成");
                            stageRsp6.setStageStateName("资方放款");
                            stageRspList.add(stageRsp6);
                            StageRsp stageRsp5 = new StageRsp();
                            stageRsp5.setStageNo(5);
                            stageRsp5.setStageState("未完成");
                            stageRsp5.setStageStateName("用户要款");
                            stageRspList.add(stageRsp5);
                            StageRsp stageRsp4 = new StageRsp();
                            stageRsp4.setStageNo(4);
                            stageRsp4.setStageState("未完成");
                            stageRsp4.setStageStateName("资方授信");
                            stageRspList.add(stageRsp4);
                            StageRsp stageRsp3 = new StageRsp();
                            stageRsp3.setStageNo(3);
                            stageRsp3.setStageState("未完成");
                            stageRsp3.setStageStateName("借款申请");
                            stageRspList.add(stageRsp3);
                            StageRsp stageRsp2 = new StageRsp();
                            stageRsp2.setStageNo(2);
                            stageRsp2.setStageState("未完成");
                            stageRsp2.setStageStateName("额度审批");
                            stageRspList.add(stageRsp2);
                            StageRsp stageRsp1 = new StageRsp();
                            stageRsp1.setStageNo(1);
                            stageRsp1.setStageState("已完成");
                            stageRsp1.setStageStateName("用户信息同步");
                            stageRspList.add(stageRsp1);
                        }
                    }
                }
            }
        }
        return stageRspList.stream().sorted(Comparator.comparing(StageRsp::getStageNo)).collect(Collectors.toList());
    }

    /**
     * 订单列表
     *
     * @param orderListReq
     * @return
     */
    @Override
    public PageInfo<OrderListRsp> queryOrderList(OrderListReq orderListReq) {
        if (StringUtil.isNotBlank(orderListReq.getStartTime())) {
            orderListReq.setStartTime(orderListReq.getStartTime() + " 00:00:00");
            orderListReq.setEndTime(orderListReq.getEndTime() + " 23:59:59");
        }
        logger.info("订单列表查询请求参数:{}", JsonUtil.toJsonString(orderListReq));
        PageHelper.startPage(orderListReq.getPageNum(), orderListReq.getPageSize());
        //查询分页数据
        Page<OrderListRsp> orderListRsps = null;
        if (StringUtil.isNotBlank(orderListReq.getPackageStatus())) {
            if (orderListReq.getPackageStatus().equals("已订购")) {
                orderListRsps = orderMapper.queryOrderByYesStatusList(orderListReq);
            } else {
                orderListRsps = orderMapper.queryOrderByNotStatusList(orderListReq);
            }
        } else {
            orderListRsps = orderMapper.queryOrderList(orderListReq);
        }

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        for (OrderListRsp orderListRsp : orderListRsps) {
            orderListRsp.setOrderId(orderListRsp.getOrderId());
            orderListRsp.setCollectTime(orderListRsp.getLoanTime()); //要款时间
            //查询结清状态
            Loan loan = loanMapper.selectByOrderId(orderListRsp.getOrderId());
            if (Objects.nonNull(loan)) {
                //查最大期数的还款计划
                RepayPlan maxRepayPlan = repayPlanMapper.findByLoanIdAndPeriod(loan.getId(), loan.getPeriods());
                String isClear = "N";
                String isLoanState = "N";
                if (maxRepayPlan != null && RepayState.REPAID.name().equals(maxRepayPlan.getCustRepayState())) {
                    isClear = "Y";
                    orderListRsp.setClearTime(sdf.format(maxRepayPlan.getUpdatedTime()));
                }

                if (LoanState.SUCCEED.name().equals(loan.getLoanState())) {
                    isLoanState = "Y";
                    orderListRsp.setLoanAmount(loan.getAmount());
                }
                orderListRsp.setIsLoanState(isLoanState);
                orderListRsp.setIsClear(isClear);
            }
            OrderEventRecord submitWithRights = orderEventRecordMapper.selectByOrderIdAndOrderEventRecord(orderListRsp.getOrderId(), "SUBMIT_WITH_RIGHTS");
            if (ObjectUtils.isNotEmpty(submitWithRights)) {
                orderListRsp.setRightsApplyTime(Date.from(submitWithRights.getEventTime().atZone(ZoneId.systemDefault()).toInstant()));
            }
        }
        //分页后数据
        PageInfo<OrderListRsp> orderListRspPageInfo = new PageInfo<>(orderListRsps);
        logger.info("分页查询订单信息响应:{}", JsonUtil.toJsonString(orderListRspPageInfo));
        return orderListRspPageInfo;
    }

    /**
     * 签章协议查询
     *
     * @param agreementFileReq
     * @return
     */
    @Override
    public List<AgreementFileRsp> querAgreementFile(AgreementFileReq agreementFileReq) {
        logger.info("签章协议下载请求参数:{}", JsonUtil.toJsonString(agreementFileReq));
        ArrayList<String> relatedIds = new ArrayList<>();
        ArrayList<String> userFileIds = new ArrayList<>();
        //查询放款信息
        Loan loan = loanMapper.selectByOrderId(agreementFileReq.getOrderId());
        if (null != loan) {
            relatedIds.add(loan.getId());
            //防止查询条件出现空
            if (loan.getLoanRecordId() != null)  relatedIds.add(loan.getLoanRecordId());

        }
        //查询订单信息
        Order order = orderMapper.selectById(agreementFileReq.getOrderId());
        relatedIds.add(order.getRiskId());
        QueryWrapper<AgreementSignRelation> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(AgreementSignRelation::getRelatedId, relatedIds);
        List<AgreementSignRelation> agreementSignRelations = agreementSignRelationMapper.selectList(queryWrapper);
        agreementSignRelations.forEach(a -> {
            userFileIds.add(a.getSignApplyId());
        });
        List<UserFile> userFiles = userFileMapper.queryUserAgreementFile(userFileIds);
        logger.info("签章协议响应:{}", JsonUtil.toJsonString(userFiles));
        List<AgreementFileRsp> agreementFileList = new ArrayList<>();
        for (UserFile userFile : userFiles) {
            if (ObjectUtils.isNotEmpty(userFile)) {
                AgreementFileRsp agreementFileRsp = new AgreementFileRsp();
                String ossUrl = fileService.getOssUrl(userFile.getOssBucket(), userFile.getOssKey());
                agreementFileRsp.setAgreementFileName(userFile.getFileName());
                agreementFileRsp.setAgreementFileUrl(ossUrl);
                agreementFileRsp.setOrderId(agreementFileReq.getOrderId());
                agreementFileRsp.setExpirationDate(LocalDate.now().plusDays(EXPIRATION_DAY).toString());
                agreementFileList.add(agreementFileRsp);
            }
        }
        logger.info("签章协议下载地址数据:{}", JsonUtil.toJsonString(agreementFileList));
        return agreementFileList;
    }

    @Override
    public com.jinghang.cash.modules.manage.RestResult<String> resetPassword(ResetPasswordReq resetPasswordReq) {
//        if (null == resetPasswordReq) {
//            return RestResult.fail(ResultCode.PARAM_ILLEGAL, "手机号不能为空");
//        }
//        logger.info("resetPassword 重置密码:{}", resetPasswordReq.getMobile());
//        resetPasswordReq.setCurrentUsername(SecurityUtils.getCurrentUsername());
//        //调用业务服务重置密码
//        RestResult<String> stringRestResult = businessPasswordResetService.passwordResetV2(resetPasswordReq);
//        logger.info("resetPassword 重置密码响应:{}", JsonUtil.toJsonString(stringRestResult));
//        if (!stringRestResult.isSuccess() || !stringRestResult.getCode().equals("000000")) {
//            return RestResult.fail(ResultCode.BIZ_ERROR, stringRestResult.getMsg());
//        }
//        return RestResult.success(stringRestResult.getData());
        return null;
    }

    @Override
    public void cancelOrder(OrderInfoReq orderInfoReq) {
        OrderParamDto orderParamDto = new OrderParamDto();
        orderParamDto.setOrderId(orderInfoReq.getOrderId());
        orderParamDto.setUpdateBy(SecurityUtils.getCurrentUsername());
        RestResult<?> result = businessOrderService.cancel(orderParamDto);

        if (!result.isSuccess()) {
            throw new RuntimeException(result.getMsg());
        }
    }

    @Override
    public CallUserVo handleCall() {
        User user = userRepository.findByUsername(SecurityUtils.getCurrentUsername());
        CallUser callUser = callUserRepository.findByUserId(user.getId());
        CallUserVo callUserVo = new CallUserVo();
        callUserVo.setCustomer(callUser.getCustomer());
        callUserVo.setUsername(callUser.getUsername());
        callUserVo.setPassword(callUser.getPassword());
        return callUserVo;
    }

    @Override
    public void callSave(CallRecordSaveReq callRecordSaveReq) {
        CallRecord callRecord = new CallRecord();
        callRecord.setPhone(callRecordSaveReq.getPhone());
        callRecord.setOrderId(callRecordSaveReq.getOrderId());
        callRecord.setCallState(CallState.valueOf(callRecordSaveReq.getCallState()).getDesc());
        callRecord.setDescription(callRecordSaveReq.getDescription());
        callRecordRepository.save(callRecord);
    }

    @Override
    public PageInfo<CallRecordVo> queryCallRecord(CallRecordQueryReq callRecordQueryReq) {
        if (callRecordQueryReq == null) {
            callRecordQueryReq = new CallRecordQueryReq();
        }
        // 设置默认分页参数
        callRecordQueryReq.setPageNum(Optional.ofNullable(callRecordQueryReq.getPageNum()).orElse(1));
        callRecordQueryReq.setPageSize(Optional.ofNullable(callRecordQueryReq.getPageSize()).orElse(10));

        // 设置排序参数，按 createTime 降序排序
        //String orderByClause = "create_time desc";
        PageHelper.startPage(callRecordQueryReq.getPageNum(), callRecordQueryReq.getPageSize());
        List<CallRecord> callRecords = callRecordRepository.findByPhoneOrderByCreateTimeDesc(callRecordQueryReq.getPhone());
        PageHelper.clearPage();
        // 防止空指针异常
        if (callRecords.isEmpty()) {
            return new PageInfo<>(Collections.emptyList());
        }

        // 使用流式操作进行字段映射
        List<CallRecordVo> callRecordVos = callRecords.stream()
                .map(this::convertToCallRecordVo)
                .collect(Collectors.toList());

        // 构建 PageInfo 对象
        PageInfo<CallRecordVo> info = new PageInfo<>(callRecordVos);
        info.setTotal(info.getTotal());
        info.setPages(info.getPages());
        info.setPageNum(callRecordQueryReq.getPageNum());
        info.setPageSize(callRecordQueryReq.getPageSize());
        return info;
    }

    private CallRecordVo convertToCallRecordVo(CallRecord callRecord) {
        CallRecordVo callRecordVo = new CallRecordVo();
        callRecordVo.setOrderId(callRecord.getOrderId());
        callRecordVo.setCallState(callRecord.getCallState());
        callRecordVo.setDescription(callRecord.getDescription());
        callRecordVo.setCreateBy(callRecord.getCreateBy());
        callRecordVo.setCreateTime(callRecord.getCreateTime());
        return callRecordVo;
    }

    public BankRepayRecord getBankRepayRecord(RepayPlan repayPlan) {
        //查询对客还款计划
        String loanId = repayPlan.getLoanId();
        Integer period = repayPlan.getPeriod();
        logger.info("还款计划查询对应对客还款记录loanId:{},period:{}", loanId, period);
        QueryWrapper<CustomRepayRecord> qw = new QueryWrapper<>();
        qw.lambda().eq(CustomRepayRecord::getLoanId, loanId).eq(CustomRepayRecord::getPeriod, period).eq(CustomRepayRecord::getRepayState, "SUCCEED")
                .orderByDesc(CustomRepayRecord::getId).last("limit 1");
        CustomRepayRecord customRepayRecord = customRepayRecordMapper.selectOne(qw);
        logger.info("还款计划查询对客还款记录:{}", JsonUtil.toJsonString(customRepayRecord));
        BankRepayRecord bankRepayRecord = null;
        if (ObjectUtils.isNotEmpty(customRepayRecord)) {
            logger.info("对客还款记录id:{}查询对资还款记录", customRepayRecord.getId());
            QueryWrapper<BankRepayRecord> qwbank = new QueryWrapper<>();
            qwbank.lambda().eq(BankRepayRecord::getSourceRecordId, customRepayRecord.getId()).eq(BankRepayRecord::getState, "SUCCEED");
            bankRepayRecord = bankRepayRecordMapper.selectOne(qwbank);
            return bankRepayRecord;
        } else {
            return bankRepayRecord;
        }
    }

    /**
     * 获取性别
     *
     * @param userId
     * @return
     */
    public String getGenderName(String userId) {
        //获取用户性别
        logger.info("获取用户性别信息入参:{}", userId);
        if (null != userId) {
            QueryWrapper<UserOcr> qw = new QueryWrapper<>();
            qw.lambda().eq(UserOcr::getUserId, userId);
            UserOcr userOcr = userOcrMapper.selectOne(qw);
            if (Objects.nonNull(userOcr)) {
                return toGender(userOcr.getCertNo());
            }
        }
        return "UNKNOWN";
    }


    private String toGender(String certNo) {
        if (StringUtils.isBlank(certNo)) {
            return "UNKNOWN";
        }
        // 身份证号倒数第二位,奇数为男性,偶数为女性
        char genderChar = certNo.charAt(certNo.length() - 2);
        // 判断奇偶性
        if (genderChar % 2 != 0) {
            return "MALE";
        } else {
            return "FEMALE";
        }
    }

}
