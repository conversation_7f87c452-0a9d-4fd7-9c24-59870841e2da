package com.jinghang.cash.modules.manage.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jinghang.cash.enums.*;
import com.jinghang.cash.exception.BadRequestException;
import com.jinghang.cash.exception.EntityNotFoundException;
import com.jinghang.cash.mapper.CapitalConfigMapper;
import com.jinghang.cash.mapper.FlowConfigMapper;
import com.jinghang.cash.mapper.FlowRouteConfigMapper;
import com.jinghang.cash.modules.manage.BizException;
import com.jinghang.cash.modules.manage.mapstruct.ManageMapstruct;
import com.jinghang.cash.modules.manage.remote.FlowCapitalConfigService;
import com.jinghang.cash.modules.manage.service.FlowRouteConfigService;
import com.jinghang.cash.modules.manage.vo.PageParam;
import com.jinghang.cash.modules.manage.vo.req.CapitalConfigRequest;
import com.jinghang.cash.modules.manage.vo.req.EnableCapitalConfigReq;
import com.jinghang.cash.modules.manage.vo.req.EnableFlowRouteConfigReq;
import com.jinghang.cash.modules.manage.vo.req.FlowChannelReq;
import com.jinghang.cash.modules.manage.vo.req.FlowProtocolChannelDetailReq;
import com.jinghang.cash.modules.manage.vo.req.FlowRouteConfigRequest;
import com.jinghang.cash.modules.manage.vo.res.BankChannelResponse;
import com.jinghang.cash.modules.manage.vo.res.CapitalConfigInfoResponse;
import com.jinghang.cash.modules.manage.vo.res.CapitalConfigResponse;
import com.jinghang.cash.modules.manage.vo.res.FlowProtocolChannelResponse;
import com.jinghang.cash.modules.manage.vo.res.FlowRouteConfigInfoResponse;
import com.jinghang.cash.modules.manage.vo.res.FlowRouteConfigPageResponse;
import com.jinghang.cash.modules.manage.vo.res.FlowRouteConfigResponse;
import com.jinghang.cash.pojo.CapitalConfig;
import com.jinghang.cash.pojo.FlowConfig;
import com.jinghang.cash.pojo.FlowRouteConfig;
import com.jinghang.cash.utils.DateUtil;
import com.jinghang.cash.utils.SecurityUtils;
import com.jinghang.cash.utils.StringUtils;
import com.jinghang.common.util.JsonUtil;
import com.jinghang.common.util.StringUtil;
import com.jinghang.ppd.api.dto.route.CapitalConfigDTO;
import com.jinghang.ppd.api.dto.route.EnableFlowConfigDTO;
import com.jinghang.ppd.api.dto.route.FlowRouteConfigDTO;
import com.jinghang.ppd.api.enums.AbleStatus;
import com.jinghang.ppd.api.enums.ValidStatus;
import org.apache.commons.compress.utils.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestBody;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 轻管后台-资金流量相关操作
 */
@Service
public class FlowRouteConfigServiceImpl implements FlowRouteConfigService {

    private static final Logger logger = LoggerFactory.getLogger(FlowRouteConfigServiceImpl.class);

    public static final Integer TEN_THOUSAND = 10000;
    @Autowired
    private CapitalConfigMapper capitalConfigMapper;

    @Autowired
    private FlowConfigMapper flowConfigMapper;

    @Autowired
    private FlowRouteConfigMapper flowRouteConfigMapper;

    @Autowired
    private FlowCapitalConfigService flowCapitalConfigService;

    @Value("${coupon.flow.channel}")
    private String couponFlowChannel;

    @Autowired
    private RedisTemplate redisTemplate;
    /**
     * 轻管后台-资金配置列表查询
     */
    @Override
    public PageInfo<CapitalConfigResponse> queryCapitalFlowPage(PageParam reqVO) {
        if (reqVO.getPageNum() == null || reqVO.getPageSize() == null) {
            throw new BadRequestException("请传递合法的分页参数");
        }
        Page page = PageHelper.startPage(reqVO.getPageNum(), reqVO.getPageSize());
        List<CapitalConfig> list = capitalConfigMapper.queryAll();
        list.forEach(entity -> {
            if (StringUtil.isBlank(entity.getUpdatedBy())) {
                entity.setUpdatedBy(entity.getCreatedBy());
            }
            if (entity.getUpdatedTime() == null) {
                entity.setUpdatedTime(entity.getCreatedTime());
            }
            entity.setLoanDayLimit(entity.getLoanDayLimit().divide(new BigDecimal(TEN_THOUSAND.toString()), 0, RoundingMode.DOWN));
            entity.setCreditDayLimit(entity.getCreditDayLimit().divide(new BigDecimal(TEN_THOUSAND.toString()), 0, RoundingMode.DOWN));
        });

        List<CapitalConfigResponse> result = ManageMapstruct.INSTANCE.copyCapitalConfigsToCapitalConfigResponses(list);
        result.forEach(x -> {
            x.setDesc(Enum.valueOf(BankChannel.class, x.getBankChannel()).getName());
        });
        PageInfo<CapitalConfigResponse> info = new PageInfo<>(result);
        info.setTotal(page.getTotal());
        info.setPages(page.getPages());
        info.setPageNum(page.getPageNum());
        info.setPageSize(page.getPageSize());
        return info;
    }

    /**
     * 轻管后台-资金配置修改
     */
    @Override
    public void updateCapitalConfig(CapitalConfigRequest reqVO) {
        logger.info("轻管后台-资金配置修改:{}", JsonUtil.toJsonString(reqVO));
        if (StringUtils.isBlank(reqVO.getId())) {
            throw new BadRequestException("id 不能为空");
        }
        //校验授信、放款、还款时间参数，并初始化时间参数
        checkParam(reqVO);
        //校验该资金方是否存在
        CapitalConfig entity = capitalConfigMapper.selectById(reqVO.getId());
        if (Objects.isNull(entity)) {
            throw new EntityNotFoundException(CapitalConfig.class, "id", reqVO.getId());
        }
        Boolean flag = checkCapital(reqVO.getBankChannel());
        if (flag && !entity.getBankChannel().equals(reqVO.getBankChannel())) {
            throw new BadRequestException("该资金方已存在,不可重复");
        }
        //拼接dto
        CapitalConfig capitalConfig = ManageMapstruct.INSTANCE.copyCapitalConfigRequestToCapitalConfig(reqVO);
        capitalConfig.setCreatedBy(entity.getCreatedBy());
        capitalConfig.setCreatedTime(entity.getCreatedTime());
        capitalConfig.setCreditDayLimit(reqVO.getCreditDayLimit().multiply(new BigDecimal(TEN_THOUSAND.toString())));
        capitalConfig.setLoanDayLimit(reqVO.getLoanDayLimit().multiply(new BigDecimal(TEN_THOUSAND.toString())));
        capitalConfig.setRevision(entity.getRevision());
        capitalConfig.setUpdatedBy(SecurityUtils.getCurrentUsername());
        capitalConfig.setUpdatedTime(LocalDateTime.now());
        CapitalConfigDTO reqDTO = ManageMapstruct.INSTANCE.copyEntityToCapitalConfigDTO(capitalConfig);
        logger.info("资金配置修改->cash-business:{}", JsonUtil.toJsonString(reqDTO));
        //TODO 此处传递下去 session 关联异常
        this.saveCapitalConfig(reqDTO);
    }

    /**
     * 此处对feiginapi异常改造迁移过来   cash-flow-manage(savle库是cash-flow主库导致session关联问题 无法操作了) ----> cash-flow
     * @param reqDTO
     */
    public void saveCapitalConfig(@RequestBody CapitalConfigDTO reqDTO) {

        LambdaQueryWrapper<CapitalConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CapitalConfig::getBankChannel, reqDTO.getBankChannel());
        CapitalConfig capitalConfig = capitalConfigMapper.selectOne(wrapper);
        CapitalConfig entity = ManageMapstruct.INSTANCE.copyCapitalConfigDTOToCapitalConfig(reqDTO);
        Optional.ofNullable(capitalConfig).ifPresentOrElse(old -> {
            entity.setGuaranteeCompany(old.getGuaranteeCompany());
            capitalConfigMapper.updateById(entity);
        },()->{
            capitalConfigMapper.insert(entity);
        });

        //删除放款额度缓存
        String capitalLimitKey = RedisKeyConstants.BIZ_LIMIT_LOAN + entity.getBankChannel() + LocalDate.now().format(DateTimeFormatter.BASIC_ISO_DATE);
        redisTemplate.delete(capitalLimitKey);
    }

    /**
     * 轻管后台-资金配置启用/禁用
     */
    @Override
    public void enableCapitalConfig(EnableCapitalConfigReq reqVO) {
        if (StringUtils.isBlank(reqVO.getId())) {
            throw new BadRequestException("id 不能为空");
        }
        CapitalConfig entity = capitalConfigMapper.selectById(reqVO.getId());
        if (Objects.isNull(entity)) {
            throw new EntityNotFoundException(CapitalConfig.class, "id", reqVO.getId());
        }
        entity.setUpdatedTime(LocalDateTime.now());
        entity.setUpdatedBy(SecurityUtils.getCurrentUsername());
        entity.setEnabled(reqVO.getEnabled());
        entity.setId(reqVO.getId());
        CapitalConfigDTO reqDTO = ManageMapstruct.INSTANCE.copyEntityToCapitalConfigDTO(entity);
        //TODO 此处传递下去 session 关联异常
        this.saveCapitalConfig(reqDTO);
    }

    /**
     * 轻管后台-资金配置详情
     */
    @Override
    public CapitalConfigInfoResponse getCapitalConfig(String id) {
        if (StringUtils.isBlank(id)) {
            throw new BadRequestException("id 不能为空");
        }
        CapitalConfig entity = capitalConfigMapper.selectById(id);
        if (Objects.isNull(entity)) {
            throw new BadRequestException("查询不到记录");
        }
        CapitalConfigInfoResponse result = ManageMapstruct.INSTANCE.copyCapitalConfigToCapitalConfigInfoResponse(entity);
        //获取可选择的资方
        List<CapitalConfig> list = capitalConfigMapper.queryAll();
        List<String> banlChannelList =
            list.stream().map(CapitalConfig::getBankChannel).distinct().filter(bankChannel -> !bankChannel.equals(entity.getBankChannel()))
                .collect(Collectors.toList());
        List<BankChannelResponse> all = getCapitalList();
        if (CollectionUtil.isNotEmpty(banlChannelList)) {
            all.removeIf(x -> banlChannelList.contains(x.getName()));
        }
        result.setBankChannelList(all);
        result.setCreditDayLimit(result.getCreditDayLimit().divide(new BigDecimal(TEN_THOUSAND.toString()), 0, RoundingMode.DOWN));
        result.setLoanDayLimit(result.getLoanDayLimit().divide(new BigDecimal(TEN_THOUSAND.toString()), 0, RoundingMode.DOWN));
        return result;
    }

    /**
     * 轻管后台-新增配置
     */
    @Override
    public void saveCapitalConfig(CapitalConfigRequest reqVO) {
        //校验授信、放款、还款时间参数,并初始化时间参数
        checkParam(reqVO);
        Boolean flag = checkCapital(reqVO.getBankChannel());
        if (flag) {
            throw new BadRequestException("该资金方已存在,不可重复");
        }
        //拼接dto
        CapitalConfig capitalConfig = ManageMapstruct.INSTANCE.copyCapitalConfigRequestToCapitalConfig(reqVO);
        capitalConfig.setCreditDayLimit(capitalConfig.getCreditDayLimit().multiply(new BigDecimal(TEN_THOUSAND.toString())));
        capitalConfig.setLoanDayLimit(capitalConfig.getLoanDayLimit().multiply(new BigDecimal(TEN_THOUSAND.toString())));
        capitalConfig.setCreatedBy(SecurityUtils.getCurrentUsername());
        capitalConfig.setCreatedTime(LocalDateTime.now());
        CapitalConfigDTO reqDTO = ManageMapstruct.INSTANCE.copyEntityToCapitalConfigDTO(capitalConfig);
        logger.info("资金配置新增->cash-business:{}", JsonUtil.toJsonString(reqDTO));
        //TODO 此处传递下去 session 关联异常
        this.saveCapitalConfig(reqDTO);
    }


    /**
     * 轻管后台- 获取所有可选择资方
     */
    @Override
    public List<BankChannelResponse> getCapitalList() {
        LambdaQueryWrapper<CapitalConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper
            .eq(CapitalConfig::getEnabled, FlowCapitalEnable.ENABLE)
            .select(CapitalConfig::getBankChannel, CapitalConfig::getId);
        List<CapitalConfig> list = capitalConfigMapper.getCapitalList();
        List<BankChannelResponse> result = ManageMapstruct.INSTANCE.copyCapitalConfigToBankChannelResponse(list);
        result.forEach(x -> {
            x.setDesc(Enum.valueOf(BankChannel.class, x.getName()).getName());
        });
        return result;
    }

    @Override
    public List<BankChannelResponse> queryCapital() {
        List<CapitalConfig> list = capitalConfigMapper.queryAll();
        boolean flag = CollectionUtil.isEmpty(list);
        list = flag ? new ArrayList<>() : list;
        List<String> bankChannelList = list.stream().map(CapitalConfig::getBankChannel).collect(Collectors.toList());
        BankChannel[] values = BankChannel.values();
        List<BankChannelResponse> result = new ArrayList<>();
        for (BankChannel value : values) {
            BankChannelResponse response = new BankChannelResponse();
            response.setName(value.toString());
            response.setDesc(value.getName());
            response.setExist(bankChannelList.contains(value.toString()));
            result.add(response);
        }
        return result;
    }

    @Override
    public List<BankChannelResponse> getCouponFlowConfig() {
        String[] split = couponFlowChannel.split(",");
        Set<String> flowConfigs = new HashSet<>(Arrays.asList(split)); // 使用HashSet来存储流配置
        List<BankChannelResponse> list = new ArrayList<>();
        FlowChannel[] flowChannels = FlowChannel.values();
        for (FlowChannel flowChannel : flowChannels) {
            if (flowConfigs.contains(flowChannel.name())) { // 直接检查是否包含
                BankChannelResponse bankChannelResponse = new BankChannelResponse();
                bankChannelResponse.setDesc(flowChannel.getDesc());
                bankChannelResponse.setName(flowChannel.name());
                list.add(bankChannelResponse);
            }
        }
        return list;
    }


    /**
     * 获取流量下的资金方
     */
    @Override
    public List<FlowRouteConfigResponse> getFlowRouteConfig(String flowId) {
        //查询流量关联的有效的资金方,包含路由禁用的和资方禁用的
        List<FlowRouteConfigResponse> result = flowRouteConfigMapper.query(flowId);
        Map<String, String> map = Arrays.stream(BankChannel.values()).collect(Collectors.toMap(BankChannel::toString, BankChannel::getName));
        result.removeIf(x -> StringUtil.isBlank(x.getBankChannel()) || !map.containsKey(x.getBankChannel()));
        if (CollectionUtil.isEmpty(result)) {
            return new ArrayList<>();
        }
        result.forEach(res -> {
            res.setValid(ValidStatus.Y.toString());
            res.setDesc(map.getOrDefault(res.getBankChannel(), ""));
        });
        return result;
    }

    /**
     * 路由详情
     */
    @Override
    public FlowRouteConfigInfoResponse getFlowRouteConfigInfo(String flowId) {
        if (StringUtils.isBlank(flowId)) {
            throw new BadRequestException("参数不能为空");
        }
        //校验该流量方是否存在
        FlowConfig entity = flowConfigMapper.selectById(flowId);
        if (Objects.isNull(entity)) {
            throw new BadRequestException("该流量库中不存在");
        }
        FlowRouteConfigInfoResponse result = new FlowRouteConfigInfoResponse();

        //已挂载的资方
        List<FlowRouteConfigResponse> selectList = getFlowRouteConfig(flowId);
        //新增时可选择的资方
        List<FlowRouteConfigResponse> availableCapitalConfigList = getAvailableCapitalConfig(flowId);

        result.setFlowChannel(Enum.valueOf(FlowChannel.class, entity.getFlowChannel()));
        result.setFlowChannelName(result.getFlowChannel().getDesc());
        result.setAvailableList(availableCapitalConfigList);
        result.setSelectedList(selectList);
        result.setFlowId(flowId);
        return result;
    }

    /**
     * 获取所有流量
     */
    @Override
    public List<BankChannelResponse> getFlowConfig() {
        LambdaQueryWrapper<FlowConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.isNotNull(FlowConfig::getId).isNotNull(FlowConfig::getFlowChannel).select(FlowConfig::getFlowChannel, FlowConfig::getId);
        List<FlowConfig> entityList = flowConfigMapper.selectList(wrapper);
        List<BankChannelResponse> list = ManageMapstruct.INSTANCE.copyFlowConfigToBankChannelResponse(entityList);
        FlowChannel[] flowChannels = FlowChannel.values();
        for (FlowChannel flowChannel : flowChannels) {
            for (BankChannelResponse flowConfig : list) {
                if (flowChannel.toString().equals(flowConfig.getName())) {
                    flowConfig.setDesc(flowChannel.getDesc());
                }
            }
        }
        return list;
    }

    /**
     * 流量方下挂载资金方
     */
    @Override
    public void updateFlowRouteConfig(FlowRouteConfigRequest reqVO) {
        FlowRouteConfigDTO reqDTO = ManageMapstruct.INSTANCE.copyFlowRouteConfigRequestToFlowRouteConfigDTO(reqVO);
        //校验参数值
        List<FlowRouteConfigDTO.FlowRouteConfigReq> reqList = reqDTO.getList();
        //校验参数中id在库中是否真是存在
        checkFlowRouteConfigReqParam(reqDTO);
        //操作人
        reqList.forEach(req -> {
            req.setOperator(SecurityUtils.getCurrentUsername());
        });
        //执行
//        RestResult<Boolean> result = flowCapitalConfigService.saveAllFlowRouteConfig(reqDTO);
//        if (!result.isSuccess() || !result.getData()) {
//            throw new BadRequestException("修改失败");
//        }
        this.saveAllFlowRouteConfig(reqDTO);
    }

    public void saveAllFlowRouteConfig(FlowRouteConfigDTO reqDTO) {
        List<FlowRouteConfigDTO.FlowRouteConfigReq> reqList = reqDTO.getList();
        if (CollectionUtils.isEmpty(reqList)) {
            throw new BizException(ResultCode.PARAM_ILLEGAL);
        }
        List<FlowRouteConfig> list = new ArrayList<>();
        List<String> ids = reqList.stream().map(FlowRouteConfigDTO.FlowRouteConfigReq::getId)
                .filter(org.apache.commons.lang3.StringUtils::isNotBlank)
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(ids)) {
            //需要更新的数据
            List<FlowRouteConfig> entityList = flowRouteConfigMapper.selectBatchIds(ids);
            entityList.forEach(x -> {
                reqList.forEach(req -> {
                    if (StringUtil.isNotBlank(req.getId()) && req.getId().equals(x.getId())) {
                        x.setUpdatedTime(LocalDateTime.now());
                        x.setUpdatedBy(req.getOperator());
                        x.setPriority(req.getPriority());
                        x.setCapitalId(req.getCapitalId());
                        x.setEnabled(FlowCapitalEnable.valueOf(req.getEnabled()));
                        x.setValid(req.getValid());
                    }
                });
            });
            entityList.forEach(flowRouteConfigMapper::updateById);
            //去除已经更新的数据
            reqList.removeIf(req -> org.apache.commons.lang3.StringUtils.isNotBlank(req.getId()));
        }
        if (CollectionUtils.isEmpty(reqList)) {
            return;
        }
        //需要创建的数据
        for (FlowRouteConfigDTO.FlowRouteConfigReq req : reqList) {
            FlowRouteConfig entity = new FlowRouteConfig();
            entity.setFlowId(reqDTO.getFlowId());
            entity.setCapitalId(req.getCapitalId());
            entity.setPriority(req.getPriority());
            entity.setEnabled(FlowCapitalEnable.valueOf(req.getEnabled()));
            entity.setCreatedTime(LocalDateTime.now());
            entity.setCreatedBy(req.getOperator());
            entity.setValid(req.getValid());
            list.add(entity);
        }
        list.forEach(flowRouteConfigMapper::insert);
    }

    /**
     * 资方路由列表
     */
    @Override
    public PageInfo<FlowRouteConfigPageResponse> queryFlowRouteConfigPage(PageParam reqVO) {
        PageHelper.startPage(reqVO.getPageNum(), reqVO.getPageSize());
        List<FlowRouteConfigPageResponse> list = flowRouteConfigMapper.queryFlowRouteConfigPage();
        list.forEach(x -> {
            if (StringUtils.isBlank(x.getUpdatedBy())) {
                x.setUpdatedBy(x.getCreatedBy());
            }
            if (x.getUpdatedTime() == null) {
                x.setUpdatedTime(x.getCreatedTime());
            }
            x.setDesc(Enum.valueOf(FlowChannel.class, x.getFlowChannel()).getDesc());
            String capitalConfigStr = x.getCapitalConfigStr();
            if (StringUtil.isNotBlank(capitalConfigStr)) {
                List<FlowRouteConfigPageResponse.CapitalConfigDTO> configDTOList = conver(capitalConfigStr);
                x.setCapitalConfigList(configDTOList);
            }
        });
        return new PageInfo<>(list);
    }

    /**
     * 流量方启用/禁用
     */
    @Override
    public void enableFlowConfig(EnableFlowRouteConfigReq reqVO) {
        String flowRouteConfigId = reqVO.getFlowConfigId();
        FlowConfig entity = flowConfigMapper.selectById(flowRouteConfigId);
        if (Objects.isNull(entity)) {
            throw new BadRequestException("流量id不合法");
        }
        //修改
        EnableFlowConfigDTO reqDTO = new EnableFlowConfigDTO();
        reqDTO.setEnabled(reqVO.getEnabled());
        reqDTO.setFlowConfigId(reqVO.getFlowConfigId());
        reqDTO.setOperator(SecurityUtils.getCurrentUsername());
//        RestResult<Boolean> result = flowCapitalConfigService.enableFlowConfig(reqDTO);
//        if (!result.isSuccess() || !result.getData()) {
//            throw new BadRequestException("操作失败");
//        }
        this.enableFlowConfig(reqDTO);
    }


    public void enableFlowConfig(EnableFlowConfigDTO reqDTO) {
        if (StringUtil.isBlank(reqDTO.getFlowConfigId()) || StringUtil.isBlank(reqDTO.getEnabled())
                || StringUtil.isBlank(reqDTO.getOperator())) {
            throw new BizException(ResultCode.PARAM_ILLEGAL);
        }
        FlowConfig flowConfig = flowConfigMapper.selectById(reqDTO.getFlowConfigId());
        Optional.ofNullable(flowConfig).orElseThrow(()->new BizException(ResultCode.QUERY_RECORDS_FAIL));
        flowConfig.setEnabled(String.valueOf(Enum.valueOf(AbleStatus.class, reqDTO.getEnabled())));
        flowConfig.setUpdatedBy(reqDTO.getOperator());
        flowConfig.setUpdatedTime(LocalDateTime.now());
        flowConfigMapper.updateById(flowConfig);
    }

    /**
     * 新增资方时获取可选资方
     */
    @Override
    public List<FlowRouteConfigResponse> getAvailableCapitalConfig(String flowId) {
        //获取所有已经启用的资金方
        List<CapitalConfig> list = capitalConfigMapper.queryAll();
        if (CollectionUtil.isEmpty(list)) {
            return new ArrayList<>();
        }
        //返回数据包含：选中过又取消的状态,选中状态，不包含无效的
        List<FlowRouteConfigResponse> result = flowRouteConfigMapper.queryFlowRouteConfig(flowId);
        //该
        if (CollectionUtil.isEmpty(result)) {
            result = ManageMapstruct.INSTANCE.copyCapitalConfigToFlowRouteConfigResponse(list);
            return result;
        }
        //没有建立过路由关系的资方也要返回，默认未选中
        List<String> capitalIds = result.stream().map(FlowRouteConfigResponse::getCapitalId).collect(Collectors.toList());
        Map<String, String> map = Arrays.stream(BankChannel.values()).collect(Collectors.toMap(BankChannel::toString, BankChannel::getName));
        for (CapitalConfig capitalConfig : list) {
            if (!capitalIds.contains(capitalConfig.getId())) {
                //添加没有勾选过
                FlowRouteConfigResponse response = new FlowRouteConfigResponse();
                response.setCapitalId(capitalConfig.getId());
                response.setSelected(Boolean.FALSE);
                response.setBankChannel(capitalConfig.getBankChannel());
                response.setEnabled(AbleStatus.ENABLE.toString());
                response.setCapitalEnabled(capitalConfig.getEnabled());
                response.setDesc(map.getOrDefault(capitalConfig.getBankChannel(), ""));
                result.add(response);
            }
        }
        result.forEach(x -> x.setDesc(Enum.valueOf(BankChannel.class, x.getBankChannel()).getName()));
        return result;
    }


    /**
     * 枚举值转换
     */
    private List<FlowRouteConfigPageResponse.CapitalConfigDTO> conver(String sourceStr) {
        String[] split = sourceStr.split(";");
        if (split.length == 0) {
            return Lists.newArrayList();
        }
        List<FlowRouteConfigPageResponse.CapitalConfigDTO> list = new ArrayList<>(split.length);
        for (String element : split) {
            FlowRouteConfigPageResponse.CapitalConfigDTO dto = new FlowRouteConfigPageResponse.CapitalConfigDTO();
            String[] capitalConfigStr = element.split("\\.");
            String str = capitalConfigStr[capitalConfigStr.length - 1];

            String[] array = str.split("-");

            String bankChannlStr = array[0];
            String name = Enum.valueOf(BankChannel.class, bankChannlStr).getName();

            dto.setCapitalCode(bankChannlStr);
            dto.setCapitalConfigStr(capitalConfigStr[0] + "." + name);
            dto.setEnabled(array[array.length - 1]);
            list.add(dto);
        }
        return list;
    }

    /**
     * 校验参数在库中是否存在
     */
    private void checkFlowRouteConfigReqParam(FlowRouteConfigDTO reqDTO) {
        List<FlowRouteConfigDTO.FlowRouteConfigReq> reqList = reqDTO.getList();
        if (CollectionUtil.isEmpty(reqList)) {
            throw new BadRequestException("请选择资金路由下的资金方");
        }
        List<String> capitalIds =
            reqList.stream().map(FlowRouteConfigDTO.FlowRouteConfigReq::getCapitalId).distinct().filter(StringUtil::isNotBlank).collect(Collectors.toList());
        List<String> flowRouteConfigIds =
            reqList.stream().map(FlowRouteConfigDTO.FlowRouteConfigReq::getId).distinct().filter(StringUtil::isNotBlank).collect(Collectors.toList());
        //校验流量资金映射关系id是否合法
        if (!checkFlowRouteConfig(flowRouteConfigIds)) {
            throw new BadRequestException("路由id有误,数据库中不存在");
        }
        //校验资金id是否合法
        if (!checkCapitalConfig(capitalIds)) {
            throw new BadRequestException("参数资金方id部分库中不存在");
        }
        //校验是否重复挂载
        List<String> checkCapitalIds =
            reqList.stream().filter(x -> StringUtil.isBlank(x.getId())).map(FlowRouteConfigDTO.FlowRouteConfigReq::getCapitalId).filter(StringUtil::isNotBlank)
                .collect(Collectors.toList());
        if (CollectionUtil.isEmpty(checkCapitalIds)) {
            return;
        }
        LambdaQueryWrapper<FlowRouteConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FlowRouteConfig::getFlowId, reqDTO.getFlowId())
            .in(FlowRouteConfig::getCapitalId, checkCapitalIds)
            .eq(FlowRouteConfig::getValid, ValidStatus.Y)
            .select(FlowRouteConfig::getId);
        boolean exists = flowRouteConfigMapper.exists(wrapper);
        if (exists) {
            throw new BadRequestException("该路由关系已存在,务重复挂载");
        }
    }

    /**
     * 判断资金方是否已存在
     * 包含所有资金方
     */
    private Boolean checkCapital(String bankChannel) {
        LambdaQueryWrapper<CapitalConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CapitalConfig::getBankChannel, bankChannel).select(CapitalConfig::getId);
        return capitalConfigMapper.exists(wrapper);
    }

    /**
     * 路由校验
     */
    private Boolean checkFlowRouteConfig(List<String> flowRouteConfigIds) {
        if (CollectionUtil.isEmpty(flowRouteConfigIds)) {
            return Boolean.TRUE;
        }
        LambdaQueryWrapper<FlowRouteConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(FlowRouteConfig::getId, flowRouteConfigIds).select(FlowRouteConfig::getId);
        Long num = flowRouteConfigMapper.selectCount(wrapper);
        return num == flowRouteConfigIds.size();
    }

    /**
     * 资方校验
     */
    private Boolean checkCapitalConfig(List<String> capitalIds) {
        LambdaQueryWrapper<CapitalConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(CapitalConfig::getId, capitalIds).select(CapitalConfig::getId);
        Long num = capitalConfigMapper.selectCount(wrapper);
        return num == capitalIds.size();
    }

    /**
     * 获取所有已启用的资方
     */
    private List<CapitalConfig> getAllCapitalEnable() {
        LambdaQueryWrapper<CapitalConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CapitalConfig::getEnabled, FlowCapitalEnable.ENABLE).select(CapitalConfig::getId, CapitalConfig::getBankChannel);
        return capitalConfigMapper.selectList(wrapper);
    }

    /**
     * 校验授信、放款、还款时间限制
     */
    private void checkParam(CapitalConfigRequest reqVO) {
        if (reqVO.getCreditTimeStatus() == null) {
            throw new BadRequestException("CreditTimeStatus 不能为空");
        }
        if (reqVO.getLoanTimeStatus() == null) {
            throw new BadRequestException("LoanTimeStatus 不能为空");
        }
        if (reqVO.getRepayTimeStatus() == null) {
            throw new BadRequestException("RepayTimeStatus 不能为空");
        }


        //是否开启 授信、放款、还款时间限制
        AbleStatus creditTimeStatus = reqVO.getCreditTimeStatus();
        AbleStatus loanTimeStatus = reqVO.getLoanTimeStatus();
        AbleStatus repayTimeStatus = reqVO.getRepayTimeStatus();

        //授信开始结束时间
        String creditStartTime = reqVO.getCreditStartTime();
        String creditEndTime = reqVO.getCreditEndTime();

        //放款开始结束时间
        String loanStartTime = reqVO.getLoanStartTime();
        String loanEndTime = reqVO.getLoanEndTime();

        //还款开始结束时间
        String repayStartTime = reqVO.getRepayStartTime();
        String repayEndTime = reqVO.getRepayEndTime();

        if (creditTimeStatus == AbleStatus.ENABLE && (StringUtils.isBlank(creditStartTime) || StringUtil.isBlank(creditEndTime))) {
            throw new BadRequestException("授信开始结束时间不能为空");
        }

        if (loanTimeStatus == AbleStatus.ENABLE && (StringUtils.isBlank(loanStartTime) || StringUtil.isBlank(loanEndTime))) {
            throw new BadRequestException("放款开始结束时间不能为空");
        }
        if (repayTimeStatus == AbleStatus.ENABLE && (StringUtils.isBlank(repayStartTime) || StringUtil.isBlank(repayEndTime))) {
            throw new BadRequestException("放款开始结束时间不能为空");
        }
        if (reqVO.getGuaranteeCompany() == null) {
            throw new BadRequestException("融担公司不能为空");
        }
        if (reqVO.getRenewedFlag() == null) {
            throw new BadRequestException("是否允许续借不能为空");
        }
        //开关关闭时初始化时间参数
        if (reqVO.getCreditTimeStatus() == AbleStatus.DISABLE) {
            reqVO.setCreditStartTime(DateUtil.getStartTimeHms());
            reqVO.setCreditEndTime(DateUtil.getEndTimeHms());
        }
        if (reqVO.getLoanTimeStatus() == AbleStatus.DISABLE) {
            reqVO.setLoanStartTime(DateUtil.getStartTimeHms());
            reqVO.setLoanEndTime(DateUtil.getEndTimeHms());
        }
        if (reqVO.getRepayTimeStatus() == AbleStatus.DISABLE) {
            reqVO.setRepayStartTime(DateUtil.getStartTimeHms());
            reqVO.setRepayEndTime(DateUtil.getEndTimeHms());
        }

    }


    @Override
    public List<FlowProtocolChannelResponse> getFlowProtocolChannelList(FlowChannelReq req) {
        List<FlowConfig> configList;
        if (StringUtil.isBlank(req.getFlowChannel())) {
            configList = flowConfigMapper.selectList(null);
        } else {
            configList = flowConfigMapper.selectList(new LambdaQueryWrapper<FlowConfig>().eq(FlowConfig::getFlowChannel, req.getFlowChannel()));
        }

        return ManageMapstruct.INSTANCE.toFlowProtocolChannelList(configList);
    }

    @Override
    public FlowProtocolChannelResponse getFlowProtocolChannelDetail(FlowChannelReq req) {
        FlowConfig flowConfig = flowConfigMapper.selectOne(new LambdaQueryWrapper<FlowConfig>().eq(FlowConfig::getFlowChannel, req.getFlowChannel()));

        return ManageMapstruct.INSTANCE.toFlowProtocolChannelDetail(flowConfig);
    }

    @Override
    public void saveFlowProtocolChannelDetail(FlowProtocolChannelDetailReq req) {
        FlowConfig flowConfig = flowConfigMapper.selectOne(new LambdaQueryWrapper<FlowConfig>().eq(FlowConfig::getFlowChannel, req.getFlowChannel()));
        flowConfig.setFirstProtocolChannel(req.getFirstProtocolChannel());
        flowConfig.setSecondProtocolChannel(req.getSecondProtocolChannel());
        flowConfig.setUpdatedBy(SecurityUtils.getCurrentUsername());
        flowConfig.setUpdatedTime(LocalDateTime.now());
        flowConfigMapper.updateById(flowConfig);
    }


}
