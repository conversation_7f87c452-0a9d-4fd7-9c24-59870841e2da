package com.jinghang.cash.enums;


public enum FlowChannel {
    LVXIN("鲸航"),
    PPCJDL("拍拍");
    private String desc;

    FlowChannel(String desc) {
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }


    public static FlowChannel getEnum(String desc) {
        for (FlowChannel channel : FlowChannel.values()) {
            if (channel.getDesc().equals(desc)) {
                return channel;
            }
        }
        return null;
    }

}
