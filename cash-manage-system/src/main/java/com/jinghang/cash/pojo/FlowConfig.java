package com.jinghang.cash.pojo;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jinghang.cash.enums.ProtocolChannel;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 流量配置
 * @TableName flow_config
 */
@Data
@TableName(value ="flow_config")
public class FlowConfig implements Serializable {
    /**
     * 主键
     */
    @TableId
    private String id;

    /**
     * 流量渠道
     */
    private String flowChannel;

    /**
     * 流量授信限额
     */
    private BigDecimal creditDayAmt;

    /**
     * 流量放款限额
     */
    private BigDecimal loanDayAmt;

    /**
     * 第一绑卡渠道
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private ProtocolChannel firstProtocolChannel;

    /**
     * 第二绑卡渠道
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private ProtocolChannel secondProtocolChannel;

    /**
     * 启用状态
     */
    private String enabled;

    /**
     * 备注
     */
    private String remark;

    /**
     * 乐观锁
     */
    private String revision;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date createdTime;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

}
