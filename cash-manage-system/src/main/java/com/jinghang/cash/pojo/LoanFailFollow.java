package com.jinghang.cash.pojo;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jinghang.cash.enums.BankChannel;
import com.jinghang.cash.enums.FlowChannel;
import com.jinghang.cash.enums.WhetherState;
import lombok.Data;

import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

@Data
@TableName(value ="loan_fail_follow")
public class LoanFailFollow implements Serializable {
    private String id;
    /**
     * 流量方订单编号
     */
    private String outerOrderId;
    /**
     * 手机号
     */
    private String mobile;
    /**
     * 身份证号
     */
    private String certNo;
    /**
     * 流量方
     */
    @Enumerated(EnumType.STRING)
    private FlowChannel flowChannel;
    /**
     * 资金方
     */
    @Enumerated(EnumType.STRING)
    private BankChannel bankChannel;
    /**
     * 申请时间
     */
    private LocalDateTime applyTime;
    /**
     *是否处理
     */
    @Enumerated(EnumType.STRING)
    private WhetherState isProcess;
    /**
     * 失败原因
     */
    private String failReason;

    private String remark;

    private String revision;

    private String createdBy;

    private Date createdTime;

    private String updatedBy;

    private Date updatedTime;


    public String getOuterOrderId() {
        return outerOrderId;
    }

    public void setOuterOrderId(String outerOrderId) {
        this.outerOrderId = outerOrderId;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getCertNo() {
        return certNo;
    }

    public void setCertNo(String certNo) {
        this.certNo = certNo;
    }

    public FlowChannel getFlowChannel() {
        return flowChannel;
    }

    public void setFlowChannel(FlowChannel flowChannel) {
        this.flowChannel = flowChannel;
    }

    public BankChannel getBankChannel() {
        return bankChannel;
    }

    public void setBankChannel(BankChannel bankChannel) {
        this.bankChannel = bankChannel;
    }

    public String getFailReason() {
        return failReason;
    }

    public void setFailReason(String failReason) {
        this.failReason = failReason;
    }

    public WhetherState getIsProcess() {
        return isProcess;
    }

    public void setIsProcess(WhetherState isProcess) {
        this.isProcess = isProcess;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getRevision() {
        return revision;
    }

    public void setRevision(String revision) {
        this.revision = revision;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Date getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(Date createdTime) {
        this.createdTime = createdTime;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public Date getUpdatedTime() {
        return updatedTime;
    }

    public void setUpdatedTime(Date updatedTime) {
        this.updatedTime = updatedTime;
    }
    public LocalDateTime getApplyTime() {
        return applyTime;
    }
    public void setApplyTime(LocalDateTime applyTime) {
        this.applyTime = applyTime;
    }
}
