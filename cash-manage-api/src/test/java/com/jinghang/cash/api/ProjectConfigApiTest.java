package com.jinghang.cash.api;

import com.jinghang.cash.api.dto.ProjectConfigDto;
import com.jinghang.cash.api.dto.RestResult;
import com.jinghang.cash.api.enums.ProjectStatus;
import com.jinghang.cash.api.enums.EnvType;
import com.jinghang.cash.api.enums.ConfigType;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

/**
 * API模块基础测试
 *
 * <AUTHOR>
 * @date 2025-01-19
 */
public class ProjectConfigApiTest {

    @Test
    public void testRestResultSuccess() {
        RestResult<String> result = RestResult.success("test data");
        
        assertTrue(result.isSuccess());
        assertFalse(result.isFail());
        assertEquals("000000", result.getCode());
        assertEquals("success", result.getMsg());
        assertEquals("test data", result.getData());
        assertNotNull(result.getTimestamp());
    }

    @Test
    public void testRestResultFail() {
        RestResult<String> result = RestResult.fail("error message");
        
        assertFalse(result.isSuccess());
        assertTrue(result.isFail());
        assertEquals("999999", result.getCode());
        assertEquals("error message", result.getMsg());
        assertNull(result.getData());
    }

    @Test
    public void testProjectConfigDto() {
        ProjectConfigDto config = new ProjectConfigDto();
        config.setProjectCode("TEST001");
        config.setProjectName("测试项目");
        config.setStatus("ENABLE");
        config.setEnvType("PROD");
        config.setEffectiveStartTime(LocalDateTime.now().minusDays(1));
        config.setEffectiveEndTime(LocalDateTime.now().plusDays(30));
        
        assertTrue(config.isEffective());
        assertTrue(config.isEnabled());
        assertEquals("TEST001", config.getProjectCode());
        assertEquals("测试项目", config.getProjectName());
    }

    @Test
    public void testProjectStatusEnum() {
        ProjectStatus enable = ProjectStatus.ENABLE;
        ProjectStatus disable = ProjectStatus.DISABLE;
        
        assertTrue(enable.isEnabled());
        assertFalse(disable.isEnabled());
        
        assertEquals("ENABLE", enable.getCode());
        assertEquals("启用", enable.getDesc());
        
        assertEquals(ProjectStatus.ENABLE, ProjectStatus.fromCode("ENABLE"));
        assertEquals(ProjectStatus.DISABLE, ProjectStatus.fromCode("DISABLE"));
    }

    @Test
    public void testEnvTypeEnum() {
        EnvType dev = EnvType.DEV;
        EnvType test = EnvType.TEST;
        EnvType prod = EnvType.PROD;
        
        assertTrue(dev.isDev());
        assertTrue(test.isTest());
        assertTrue(prod.isProd());
        
        assertFalse(prod.isDev());
        assertFalse(dev.isProd());
    }

    @Test
    public void testConfigTypeEnum() {
        ConfigType channel = ConfigType.CHANNEL;
        ConfigType risk = ConfigType.RISK;
        
        assertEquals("CHANNEL", channel.getCode());
        assertEquals("渠道配置", channel.getDesc());
        
        assertEquals(ConfigType.CHANNEL, ConfigType.fromCode("CHANNEL"));
        assertEquals(ConfigType.RISK, ConfigType.fromCode("RISK"));
    }
}
