package com.jinghang.cash.api;

import com.jinghang.cash.api.dto.ProjectInfoDto;
import com.jinghang.cash.api.dto.RestResult;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

/**
 * API模块基础测试
 *
 * <AUTHOR>
 * @date 2025-01-19
 */
public class ProjectInfoApiTest {

    @Test
    public void testRestResultSuccess() {
        RestResult<String> result = RestResult.success("test data");

        assertTrue(result.isSuccess());
        assertFalse(result.isFail());
        assertEquals("000000", result.getCode());
        assertEquals("success", result.getMsg());
        assertEquals("test data", result.getData());
        assertNotNull(result.getTimestamp());
    }

    @Test
    public void testRestResultFail() {
        RestResult<String> result = RestResult.fail("error message");

        assertFalse(result.isSuccess());
        assertTrue(result.isFail());
        assertEquals("999999", result.getCode());
        assertEquals("error message", result.getMsg());
        assertNull(result.getData());
    }

    @Test
    public void testProjectInfoDto() {
        ProjectInfoDto projectInfo = new ProjectInfoDto();
        projectInfo.setProjectCode("TEST001");
        projectInfo.setProjectName("测试项目");
        projectInfo.setEnabled("ENABLE");
        projectInfo.setFlowChannel("FQL");
        projectInfo.setStartDate(LocalDate.now().minusDays(10));
        projectInfo.setEndDate(LocalDate.now().plusDays(30));

        assertTrue(projectInfo.isEnabled());
        assertTrue(projectInfo.isInValidPeriod());
        assertTrue(projectInfo.isValid());
        assertEquals("TEST001", projectInfo.getProjectCode());
        assertEquals("测试项目", projectInfo.getProjectName());
    }

    @Test
    public void testProjectElementsDto() {
        ProjectInfoDto.ProjectElementsDto elements = new ProjectInfoDto.ProjectElementsDto();
        elements.setDrawableAmountRange("1000-50000");
        elements.setDrawableAmountStep("1000");
        elements.setCreditDarkHours("23:00-06:00");
        elements.setDailyCreditLimit(new BigDecimal("100"));
        elements.setCustomerInterestRate("24.0");
        elements.setProjectDurationType("LONGTIME");
        elements.setEnabled("ENABLE");

        assertEquals("1000-50000", elements.getDrawableAmountRange());
        assertEquals("24.0", elements.getCustomerInterestRate());
        assertEquals("LONGTIME", elements.getProjectDurationType());
    }

    @Test
    public void testProjectElementsExtDto() {
        ProjectInfoDto.ProjectElementsExtDto elementsExt = new ProjectInfoDto.ProjectElementsExtDto();
        elementsExt.setInterestDaysBasis("360");
        elementsExt.setAllowCrossDayRepay("Y");
        elementsExt.setRiskModelChannel("RISK001");
        elementsExt.setLoanPaymentChannel("PAY001");
        elementsExt.setGracePeriodType("SQ");
        elementsExt.setGracePeriodDays("3");

        assertEquals("360", elementsExt.getInterestDaysBasis());
        assertEquals("Y", elementsExt.getAllowCrossDayRepay());
        assertEquals("SQ", elementsExt.getGracePeriodType());
    }

    @Test
    public void testProjectInfoValidation() {
        ProjectInfoDto projectInfo = new ProjectInfoDto();

        // 测试未启用的项目
        projectInfo.setEnabled("DISABLE");
        projectInfo.setStartDate(LocalDate.now().minusDays(10));
        projectInfo.setEndDate(LocalDate.now().plusDays(30));

        assertFalse(projectInfo.isEnabled());
        assertTrue(projectInfo.isInValidPeriod());
        assertFalse(projectInfo.isValid());

        // 测试已过期的项目
        projectInfo.setEnabled("ENABLE");
        projectInfo.setStartDate(LocalDate.now().minusDays(30));
        projectInfo.setEndDate(LocalDate.now().minusDays(1));

        assertTrue(projectInfo.isEnabled());
        assertFalse(projectInfo.isInValidPeriod());
        assertFalse(projectInfo.isValid());

        // 测试未开始的项目
        projectInfo.setStartDate(LocalDate.now().plusDays(1));
        projectInfo.setEndDate(LocalDate.now().plusDays(30));

        assertTrue(projectInfo.isEnabled());
        assertFalse(projectInfo.isInValidPeriod());
        assertFalse(projectInfo.isValid());
    }
}
