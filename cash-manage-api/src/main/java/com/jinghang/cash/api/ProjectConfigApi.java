package com.jinghang.cash.api;

import com.jinghang.cash.api.dto.ProjectConfigDetailDto;
import com.jinghang.cash.api.dto.ProjectConfigDto;
import com.jinghang.cash.api.dto.RestResult;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

/**
 * 项目配置API接口
 * 
 * <AUTHOR>
 * @date 2025-01-19
 */
public interface ProjectConfigApi {

    /**
     * 根据项目编码获取项目配置
     *
     * @param projectCode 项目编码
     * @return 项目配置信息
     */
    @GetMapping("/config/project/{projectCode}")
    RestResult<ProjectConfigDto> getProjectConfig(@PathVariable("projectCode") String projectCode);

    /**
     * 根据流量渠道获取项目配置列表
     *
     * @param flowChannel 流量渠道
     * @return 项目配置列表
     */
    @GetMapping("/config/project/list")
    RestResult<List<ProjectConfigDto>> getProjectConfigsByChannel(@RequestParam("flowChannel") String flowChannel);

    /**
     * 批量获取项目配置
     *
     * @param projectCodes 项目编码列表
     * @return 项目编码与配置的映射
     */
    @PostMapping("/config/project/batch")
    RestResult<Map<String, ProjectConfigDto>> batchGetProjectConfigs(@RequestBody List<String> projectCodes);

    /**
     * 获取项目配置详情（包含配置项）
     *
     * @param projectCode 项目编码
     * @return 项目配置详情
     */
    @GetMapping("/config/project/{projectCode}/detail")
    RestResult<ProjectConfigDetailDto> getProjectConfigDetail(@PathVariable("projectCode") String projectCode);

    /**
     * 根据业务类型和环境获取项目配置
     *
     * @param businessType 业务类型
     * @param envType 环境类型
     * @return 项目配置列表
     */
    @GetMapping("/config/project/query")
    RestResult<List<ProjectConfigDto>> queryProjectConfigs(@RequestParam("businessType") String businessType,
                                                          @RequestParam("envType") String envType);

    /**
     * 检查项目配置是否存在
     *
     * @param projectCode 项目编码
     * @return 是否存在
     */
    @GetMapping("/config/project/{projectCode}/exists")
    RestResult<Boolean> existsProjectConfig(@PathVariable("projectCode") String projectCode);

    /**
     * 获取所有有效的项目配置
     *
     * @return 有效的项目配置列表
     */
    @GetMapping("/config/project/active")
    RestResult<List<ProjectConfigDto>> getActiveProjectConfigs();
}
