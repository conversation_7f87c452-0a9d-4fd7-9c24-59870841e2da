package com.jinghang.cash.api.enums;

/**
 * 环境类型枚举
 *
 * <AUTHOR>
 * @date 2025-01-19
 */
public enum EnvType {

    /**
     * 开发环境
     */
    DEV("DEV", "开发环境"),

    /**
     * 测试环境
     */
    TEST("TEST", "测试环境"),

    /**
     * 预生产环境
     */
    UAT("UAT", "预生产环境"),

    /**
     * 生产环境
     */
    PROD("PROD", "生产环境");

    private final String code;
    private final String desc;

    EnvType(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据代码获取枚举
     */
    public static EnvType fromCode(String code) {
        for (EnvType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown env type code: " + code);
    }

    /**
     * 判断是否为生产环境
     */
    public boolean isProd() {
        return this == PROD;
    }

    /**
     * 判断是否为测试环境
     */
    public boolean isTest() {
        return this == TEST || this == UAT;
    }

    /**
     * 判断是否为开发环境
     */
    public boolean isDev() {
        return this == DEV;
    }
}
