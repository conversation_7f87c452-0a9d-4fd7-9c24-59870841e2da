package com.jinghang.cash.api;

import com.jinghang.cash.api.dto.ProjectInfoDto;
import com.jinghang.cash.api.dto.RestResult;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

/**
 * 项目信息API接口
 * 
 * <AUTHOR>
 * @date 2025-01-19
 */
public interface ProjectInfoApi {

    /**
     * 根据项目编码查询项目完整信息
     *
     * @param projectCode 项目编码
     * @return 项目完整信息
     */
    @GetMapping("/project/info/{projectCode}")
    ProjectInfoDto queryProjectInfo(@PathVariable("projectCode") String projectCode);
}
