package com.jinghang.cash.api.enums;

/**
 * 配置类型枚举
 *
 * <AUTHOR>
 * @date 2025-01-19
 */
public enum ConfigType {

    /**
     * 渠道配置
     */
    CHANNEL("CHANNEL", "渠道配置"),

    /**
     * 风控配置
     */
    RISK("RISK", "风控配置"),

    /**
     * 资金配置
     */
    CAPITAL("CAPITAL", "资金配置"),

    /**
     * 业务参数配置
     */
    BUSINESS("BUSINESS", "业务参数配置"),

    /**
     * 系统配置
     */
    SYSTEM("SYSTEM", "系统配置"),

    /**
     * 第三方配置
     */
    THIRD_PARTY("THIRD_PARTY", "第三方配置"),

    /**
     * 数据库配置
     */
    DATABASE("DATABASE", "数据库配置"),

    /**
     * 缓存配置
     */
    CACHE("CACHE", "缓存配置"),

    /**
     * 消息队列配置
     */
    MQ("MQ", "消息队列配置"),

    /**
     * 文件存储配置
     */
    FILE_STORAGE("FILE_STORAGE", "文件存储配置"),

    /**
     * 其他配置
     */
    OTHER("OTHER", "其他配置");

    private final String code;
    private final String desc;

    ConfigType(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据代码获取枚举
     */
    public static ConfigType fromCode(String code) {
        for (ConfigType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown config type code: " + code);
    }
}
