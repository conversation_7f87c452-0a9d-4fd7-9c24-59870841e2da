package com.jinghang.cash.api.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.io.Serializable;

/**
 * 统一返回结果
 *
 * <AUTHOR>
 * @date 2025-01-19
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class RestResult<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 成功状态码
     */
    public static final String SUCCESS_CODE = "000000";

    /**
     * 失败状态码
     */
    public static final String FAIL_CODE = "999999";

    /**
     * 状态码
     */
    private String code;

    /**
     * 消息
     */
    private String msg;

    /**
     * 数据
     */
    private T data;

    /**
     * 时间戳
     */
    private Long timestamp;

    public RestResult() {
        this.timestamp = System.currentTimeMillis();
    }

    public RestResult(String code, String msg) {
        this();
        this.code = code;
        this.msg = msg;
    }

    public RestResult(String code, String msg, T data) {
        this(code, msg);
        this.data = data;
    }

    /**
     * 成功返回
     */
    public static <T> RestResult<T> success() {
        return new RestResult<>(SUCCESS_CODE, "success");
    }

    /**
     * 成功返回带数据
     */
    public static <T> RestResult<T> success(T data) {
        return new RestResult<>(SUCCESS_CODE, "success", data);
    }

    /**
     * 成功返回带消息和数据
     */
    public static <T> RestResult<T> success(String msg, T data) {
        return new RestResult<>(SUCCESS_CODE, msg, data);
    }

    /**
     * 失败返回
     */
    public static <T> RestResult<T> fail(String msg) {
        return new RestResult<>(FAIL_CODE, msg);
    }

    /**
     * 失败返回带状态码
     */
    public static <T> RestResult<T> fail(String code, String msg) {
        return new RestResult<>(code, msg);
    }

    /**
     * 失败返回带数据
     */
    public static <T> RestResult<T> fail(String code, String msg, T data) {
        return new RestResult<>(code, msg, data);
    }

    /**
     * 判断是否成功
     */
    public boolean isSuccess() {
        return SUCCESS_CODE.equals(this.code);
    }

    /**
     * 判断是否失败
     */
    public boolean isFail() {
        return !isSuccess();
    }
}
