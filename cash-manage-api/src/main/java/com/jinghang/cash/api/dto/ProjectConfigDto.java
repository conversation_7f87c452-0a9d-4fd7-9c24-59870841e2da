package com.jinghang.cash.api.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 项目配置DTO
 *
 * <AUTHOR>
 * @date 2025-01-19
 */
@Data
public class ProjectConfigDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private String id;

    /**
     * 项目编码
     */
    @NotBlank(message = "项目编码不能为空")
    private String projectCode;

    /**
     * 项目名称
     */
    @NotBlank(message = "项目名称不能为空")
    private String projectName;

    /**
     * 项目描述
     */
    private String projectDesc;

    /**
     * 项目状态 (ENABLE/DISABLE)
     */
    @NotBlank(message = "项目状态不能为空")
    private String status;

    /**
     * 流量渠道 (FQL/LVXIN/NFSP等)
     */
    private String flowChannel;

    /**
     * 业务类型 (CREDIT/LOAN/REPAY等)
     */
    private String businessType;

    /**
     * 环境类型 (DEV/TEST/PROD)
     */
    @NotBlank(message = "环境类型不能为空")
    private String envType;

    /**
     * 优先级 (数字越小优先级越高)
     */
    private Integer priority;

    /**
     * 生效开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime effectiveStartTime;

    /**
     * 生效结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime effectiveEndTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 版本号
     */
    private String revision;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createdTime;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updatedTime;

    /**
     * 判断配置是否在有效期内
     */
    public boolean isEffective() {
        LocalDateTime now = LocalDateTime.now();
        
        // 检查开始时间
        if (effectiveStartTime != null && now.isBefore(effectiveStartTime)) {
            return false;
        }
        
        // 检查结束时间
        if (effectiveEndTime != null && now.isAfter(effectiveEndTime)) {
            return false;
        }
        
        return "ENABLE".equals(status);
    }

    /**
     * 判断是否启用状态
     */
    public boolean isEnabled() {
        return "ENABLE".equals(status);
    }
}
