package com.jinghang.cash.api.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 项目配置详情DTO
 *
 * <AUTHOR>
 * @date 2025-01-19
 */
@Data
public class ProjectConfigDetailDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 项目基本配置
     */
    private ProjectConfigDto projectConfig;

    /**
     * 配置项列表
     */
    private List<ConfigItemDto> configItems;

    /**
     * 配置项映射（key-value形式，便于快速查找）
     */
    private Map<String, String> configMap;

    /**
     * 按类型分组的配置项
     */
    private Map<String, List<ConfigItemDto>> configsByType;

    /**
     * 配置项DTO
     */
    @Data
    public static class ConfigItemDto implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 配置项ID
         */
        private String id;

        /**
         * 项目ID
         */
        private String projectId;

        /**
         * 配置键
         */
        private String configKey;

        /**
         * 配置值
         */
        private String configValue;

        /**
         * 配置类型
         */
        private String configType;

        /**
         * 配置描述
         */
        private String configDesc;

        /**
         * 是否加密
         */
        private Boolean isEncrypted;

        /**
         * 排序
         */
        private Integer sortOrder;

        /**
         * 创建人
         */
        private String createdBy;

        /**
         * 创建时间
         */
        private String createdTime;

        /**
         * 更新人
         */
        private String updatedBy;

        /**
         * 更新时间
         */
        private String updatedTime;
    }

    /**
     * 根据配置键获取配置值
     *
     * @param key 配置键
     * @return 配置值
     */
    public String getConfigValue(String key) {
        return configMap != null ? configMap.get(key) : null;
    }

    /**
     * 根据配置键获取配置值，如果不存在则返回默认值
     *
     * @param key 配置键
     * @param defaultValue 默认值
     * @return 配置值
     */
    public String getConfigValue(String key, String defaultValue) {
        String value = getConfigValue(key);
        return value != null ? value : defaultValue;
    }

    /**
     * 根据配置类型获取配置项列表
     *
     * @param configType 配置类型
     * @return 配置项列表
     */
    public List<ConfigItemDto> getConfigsByType(String configType) {
        return configsByType != null ? configsByType.get(configType) : null;
    }

    /**
     * 检查是否包含指定的配置键
     *
     * @param key 配置键
     * @return 是否包含
     */
    public boolean containsConfig(String key) {
        return configMap != null && configMap.containsKey(key);
    }
}
