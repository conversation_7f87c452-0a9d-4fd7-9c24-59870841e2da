# Cash-Manage API 模块

## 📋 模块说明

Cash-Manage API 模块是项目信息管理的API接口定义模块，为Flow和Capital系统提供统一的项目信息查询接口。

## 🎯 主要功能

- **API接口定义**: 定义项目信息查询的REST API接口
- **数据传输对象**: 提供标准化的DTO类，包含项目基本信息、要素信息和扩展信息
- **统一返回格式**: 提供标准的RestResult返回格式

## 📦 包结构

```
com.jinghang.cash.api/
├── ProjectInfoApi.java               # 项目信息API接口
└── dto/                              # 数据传输对象
    ├── RestResult.java              # 统一返回结果
    └── ProjectInfoDto.java          # 项目信息DTO（包含要素和扩展信息）
```

## 🔧 使用方式

### 1. 在其他模块中引用

```xml
<dependency>
    <groupId>com.jinghang.cash</groupId>
    <artifactId>cash-manage-api</artifactId>
    <version>1.0</version>
</dependency>
```

### 2. 实现API接口

```java
@RestController
@RequestMapping("/api")
public class ProjectInfoController implements ProjectInfoApi {
    // 实现接口方法
}
```

### 3. 创建Feign客户端

```java
@FeignClient(name = "cash-manage", path = "/api")
public interface ProjectInfoClient extends ProjectInfoApi {
}
```

## 📝 API接口说明

### 主要接口

- `queryProjectInfo(String projectCode)`: 根据项目编码查询项目完整信息（包含基本信息、要素信息、扩展信息）

### 返回格式

所有接口统一使用 `RestResult<T>` 格式返回：

```json
{
  "code": "000000",
  "msg": "success",
  "data": {
    "projectCode": "TEST001",
    "projectName": "测试项目",
    "flowChannel": "FQL",
    "capitalChannel": "CYBK",
    "enabled": "ENABLE",
    "elements": {
      "drawableAmountRange": "1000-50000",
      "customerInterestRate": "24.0",
      "projectDurationType": "LONGTIME"
    },
    "elementsExt": {
      "riskModelChannel": "RISK001",
      "loanPaymentChannel": "PAY001"
    }
  },
  "timestamp": 1642678800000
}
```

## 🔍 注意事项

1. **版本兼容**: API接口变更时需要考虑向后兼容性
2. **依赖最小化**: 只引入必要的依赖，保持模块轻量
3. **接口设计**: 遵循RESTful设计原则
4. **数据验证**: 使用Bean Validation注解进行参数校验
5. **数据结构**: 基于Flow系统现有的ProjectInfoVO结构设计

## 📊 依赖关系

- **Spring Web**: 提供@RequestMapping等注解
- **Common Util**: 通用工具类
- **Validation**: 参数校验注解
- **Lombok**: 简化代码编写
- **Jackson**: JSON序列化注解

## 🔄 与Flow系统的对应关系

| API模块 | Flow系统 | 说明 |
|---------|----------|------|
| ProjectInfoDto | ProjectInfoVO | 项目基本信息 |
| ProjectElementsDto | ProjectElements | 项目要素信息 |
| ProjectElementsExtDto | ProjectElementsExt | 项目要素扩展信息 |
| queryProjectInfo() | ProjectInfoService.queryProjectInfo() | 查询方法 |

---

*模块版本: v1.0*  
*创建时间: 2025-01-19*  
*维护团队: 金航科技开发团队*
