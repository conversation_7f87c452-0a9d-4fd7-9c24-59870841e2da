# Cash-Manage API 模块

## 📋 模块说明

Cash-Manage API 模块是项目配置管理的API接口定义模块，为Flow和Capital系统提供统一的项目配置查询接口。

## 🎯 主要功能

- **API接口定义**: 定义项目配置相关的REST API接口
- **数据传输对象**: 提供标准化的DTO类
- **枚举定义**: 定义项目状态、配置类型等枚举
- **统一返回格式**: 提供标准的RestResult返回格式

## 📦 包结构

```
com.jinghang.cash.api/
├── ProjectConfigApi.java              # 项目配置API接口
├── dto/                               # 数据传输对象
│   ├── RestResult.java               # 统一返回结果
│   ├── ProjectConfigDto.java         # 项目配置DTO
│   └── ProjectConfigDetailDto.java   # 项目配置详情DTO
└── enums/                            # 枚举定义
    ├── ProjectStatus.java            # 项目状态枚举
    ├── ConfigType.java              # 配置类型枚举
    └── EnvType.java                 # 环境类型枚举
```

## 🔧 使用方式

### 1. 在其他模块中引用

```xml
<dependency>
    <groupId>com.jinghang.cash</groupId>
    <artifactId>cash-manage-api</artifactId>
    <version>1.0</version>
</dependency>
```

### 2. 实现API接口

```java
@RestController
@RequestMapping("/api")
public class ProjectConfigController implements ProjectConfigApi {
    // 实现接口方法
}
```

### 3. 创建Feign客户端

```java
@FeignClient(name = "cash-manage", path = "/api")
public interface ProjectConfigClient extends ProjectConfigApi {
}
```

## 📝 API接口说明

### 主要接口

- `getProjectConfig(String projectCode)`: 根据项目编码获取配置
- `getProjectConfigsByChannel(String flowChannel)`: 根据流量渠道获取配置列表
- `batchGetProjectConfigs(List<String> projectCodes)`: 批量获取项目配置
- `getProjectConfigDetail(String projectCode)`: 获取项目配置详情
- `queryProjectConfigs(String businessType, String envType)`: 根据业务类型和环境查询配置

### 返回格式

所有接口统一使用 `RestResult<T>` 格式返回：

```json
{
  "code": "000000",
  "msg": "success", 
  "data": {...},
  "timestamp": 1642678800000
}
```

## 🔍 注意事项

1. **版本兼容**: API接口变更时需要考虑向后兼容性
2. **依赖最小化**: 只引入必要的依赖，保持模块轻量
3. **接口设计**: 遵循RESTful设计原则
4. **数据验证**: 使用Bean Validation注解进行参数校验

## 📊 依赖关系

- **Spring Web**: 提供@RequestMapping等注解
- **Common Util**: 通用工具类
- **Validation**: 参数校验注解
- **Lombok**: 简化代码编写
- **Jackson**: JSON序列化注解

---

*模块版本: v1.0*  
*创建时间: 2025-01-19*  
*维护团队: 金航科技开发团队*
