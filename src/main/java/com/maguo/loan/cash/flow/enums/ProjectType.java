package com.maguo.loan.cash.flow.enums;

/**
 * 项目类型
 */
public enum ProjectType {
    RD001("RD001", "融担类"),
    QY001("QY001", "权益类"),
    RD002("RD002", "融担类-KOO"),
    RD003("RD003", "融担类-小辉付");


    private final String code;

    private final String desc;

    ProjectType(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }
}
