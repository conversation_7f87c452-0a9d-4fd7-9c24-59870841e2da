package com.maguo.loan.cash.flow.repository;

import com.maguo.loan.cash.flow.entity.common.ProjectContractFlow;
import com.maguo.loan.cash.flow.enums.AbleStatus;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

/**
 * 项目资产合同Repository
 *
 * @Author: Lior
 * @CreateTime: 2025/8/18 15:03
 */
public interface ProjectContractFlowRepository extends JpaRepository<ProjectContractFlow, Long> {

    /**
     * 根据项目编码查询项目资产合同
     *
     * @param projectCode 项目编码
     * @return 项目资产合同
     */
    ProjectContractFlow findByProjectCodeAndEnabled(String projectCode, AbleStatus enabled);

    /**
     * 根据项目编码和状态查询项目资产合同列表
     *
     * @param projectCode 项目编码
     * @param enabled 合同状态
     * @return 项目资产合同列表
     */
    List<ProjectContractFlow> findProjectContractFlowsByProjectCodeAndEnabled(String projectCode, AbleStatus enabled);

    /**
     * 根据合同模板编码查询项目资产合同
     * @param agreementCode
     * @param ableStatus
     * @return
     */
    ProjectContractFlow findProjectContractFlowsByAgreementCodeAndEnabled(String agreementCode, AbleStatus ableStatus);
}
