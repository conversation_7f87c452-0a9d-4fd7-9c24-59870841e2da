package com.maguo.loan.cash.flow.enums;

public enum RepaymentType {
    /**
     * 等额本息
     */
    EQUAL_INSTALLMENT("EQUAL_INSTALLMENT","等额本息"),

    /**
     * 等额本金
     */
    EQUAL_PRINCIPAL("EQUAL_PRINCIPAL","等额本金"),

    /**
     * 先息后本
     */
    INTEREST_FIRST("INTEREST_FIRST","先息后本"),

    /**
     * 等本等费
     */
    EQUAL_PRINCIPAL_AND_FEE("EQUAL_PRINCIPAL_AND_FEE","等本等费");

    private final String code;

    private final String desc;

    RepaymentType(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }


    public String getDesc() {
        return desc;
    }
}
