package com.maguo.loan.cash.flow.enums;

public enum CapitalRoute {

    /**
     * 直连
     */
    DIRECT("DIRECT","直连"),

    /**
     * 路由
     */
    ROUTE("ROUTE","路由");

    private final String code;

    private final String desc;

    CapitalRoute(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }


    public String getDesc() {
        return desc;
    }
}
