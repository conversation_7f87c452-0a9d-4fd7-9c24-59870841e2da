package com.maguo.loan.cash.flow.service;

import com.alibaba.fastjson2.JSON;
import com.jinghang.capital.api.dto.BankChannel;
import com.jinghang.capital.api.dto.ProcessStatus;
import com.jinghang.capital.api.dto.ProtocolChannel;
import com.jinghang.capital.api.dto.RestResult;
import com.jinghang.capital.api.dto.repay.RepayApplyDto;
import com.jinghang.capital.api.dto.repay.RepayQueryDto;
import com.jinghang.capital.api.dto.repay.RepayResultDto;
import com.jinghang.common.util.JsonUtil;
import com.jinghang.common.util.StringUtil;
import com.maguo.loan.cash.flow.common.BizException;
import com.maguo.loan.cash.flow.common.ResultCode;
import com.maguo.loan.cash.flow.convert.EnumConvert;
import com.maguo.loan.cash.flow.convert.ManageConvert;
import com.maguo.loan.cash.flow.convert.RepayConvert;
import com.maguo.loan.cash.flow.dto.CallBackDTO;
import com.maguo.loan.cash.flow.dto.OfflineRepayApplyRequest;
import com.maguo.loan.cash.flow.dto.OnlineRepayApplyRequest;
import com.maguo.loan.cash.flow.dto.repay.OnlineRepayRequestDto;
import com.maguo.loan.cash.flow.dto.repay.OnlineRepayResponseDto;
import com.maguo.loan.cash.flow.entity.BankRepayRecord;
import com.maguo.loan.cash.flow.entity.CustomRepayRecord;
import com.maguo.loan.cash.flow.entity.Loan;
import com.maguo.loan.cash.flow.entity.OfflineRepayApply;
import com.maguo.loan.cash.flow.entity.OfflineRepayReduce;
import com.maguo.loan.cash.flow.entity.OutWithholdFlow;
import com.maguo.loan.cash.flow.entity.OutWithholdOutInfo;
import com.maguo.loan.cash.flow.entity.OutWithholdShareInfo;
import com.maguo.loan.cash.flow.entity.RepayExtraGuaranteePlan;
import com.maguo.loan.cash.flow.entity.RepayPlan;
import com.maguo.loan.cash.flow.entity.UserBankCard;
import com.maguo.loan.cash.flow.entity.UserInfo;
import com.maguo.loan.cash.flow.entity.WithholdFlow;
import com.maguo.loan.cash.flow.entity.common.ProjectElementsExt;
import com.maguo.loan.cash.flow.entrance.common.constant.LvxinSysTimeMockService;
import com.maguo.loan.cash.flow.entrance.common.service.ProjectInfoService;
import com.maguo.loan.cash.flow.enums.ActiveInactive;
import com.maguo.loan.cash.flow.enums.AuditStatus;
import com.maguo.loan.cash.flow.enums.CallbackState;
import com.maguo.loan.cash.flow.enums.ChargeBizType;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.enums.IsIncludingEquity;
import com.maguo.loan.cash.flow.enums.OuterFlag;
import com.maguo.loan.cash.flow.enums.PaySide;
import com.maguo.loan.cash.flow.enums.Payee;
import com.maguo.loan.cash.flow.enums.PaymentChannel;
import com.maguo.loan.cash.flow.enums.ProcessState;
import com.maguo.loan.cash.flow.enums.ReduceItem;
import com.maguo.loan.cash.flow.enums.QhBank;
import com.maguo.loan.cash.flow.enums.RepayMode;
import com.maguo.loan.cash.flow.enums.RepayPurpose;
import com.maguo.loan.cash.flow.enums.RepayRecordType;
import com.maguo.loan.cash.flow.enums.RepayState;
import com.maguo.loan.cash.flow.enums.RepayType;
import com.maguo.loan.cash.flow.enums.UseState;
import com.maguo.loan.cash.flow.enums.WhetherDiff;
import com.maguo.loan.cash.flow.enums.WhetherState;
import com.maguo.loan.cash.flow.remote.core.FinRepayService;
import com.maguo.loan.cash.flow.repository.BankRepayRecordRepository;
import com.maguo.loan.cash.flow.repository.CustomRepayRecordRepository;
import com.maguo.loan.cash.flow.repository.LoanRepository;
import com.maguo.loan.cash.flow.repository.OfflineRepayApplyRepository;
import com.maguo.loan.cash.flow.repository.OfflineRepayReduceRepository;
import com.maguo.loan.cash.flow.repository.OrderRepository;
import com.maguo.loan.cash.flow.repository.OutWithholdFlowRepository;
import com.maguo.loan.cash.flow.repository.OutWithholdOutInfoRepository;
import com.maguo.loan.cash.flow.repository.OutWithholdShareInfoRepository;
import com.maguo.loan.cash.flow.repository.RepayExtraGuaranteePlanRepository;
import com.maguo.loan.cash.flow.repository.RepayPlanRepository;
import com.maguo.loan.cash.flow.repository.UserBankCardRepository;
import com.maguo.loan.cash.flow.repository.UserInfoRepository;
import com.maguo.loan.cash.flow.repository.WithholdFlowRepository;
import com.maguo.loan.cash.flow.service.event.PrincipalRepaySucceedEvent;
import com.maguo.loan.cash.flow.service.event.RepaySucceedResultEvent;
import com.maguo.loan.cash.flow.util.AmountUtil;
import com.maguo.loan.cash.flow.util.BaseConstants;
import com.maguo.loan.cash.flow.util.DateUtil;
import com.maguo.loan.cash.flow.vo.TrialResultVo;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 还款服务
 */
@Service
public class RepayService {

    private static final Logger logger = LoggerFactory.getLogger(RepayService.class);

    private RepayPlanRepository repayPlanRepository;

    private LoanRepository loanRepository;

    private TrialService trialService;

    private ChargeService chargeService;

    private CustomRepayRecordRepository recordRepository;

    private BankRepayRecordRepository bankRepayRecordRepository;

    @Autowired
    private WithholdFlowRepository withholdFlowRepository;
    @Autowired
    private OutWithholdFlowRepository outWithholdFlowRepository;
    @Autowired
    private OutWithholdOutInfoRepository outWithholdOutInfoRepository;
    @Autowired
    private OutWithholdShareInfoRepository outWithholdShareInfoRepository;

    @Autowired
    private FinRepayService finRepayService;

    @Autowired
    private MqService mqService;
    @Autowired
    LvxinSysTimeMockService lvxinSysTimeMockService;
    @Autowired
    private LockService lockService;

    @Autowired
    private ApplicationEventPublisher eventPublisher;

    @Autowired
    private UserInfoRepository userInfoRepository;

    @Autowired
    private CacheService cacheService;

    @Autowired
    private PlatformOnceBoundService platformOnceBoundService;

    @Value("${repay.maintain.start}")
    private String repayMaintainStart;

    @Value("${repay.maintain.end}")
    private String repayMaintainEnd;

    @Value("${repay.maintain.msg}")
    private String repayMaintainMsg;

    @Value("${baofu.alarm.interval}")
    private String baofuAlarmInterval;

    public static final int LOCK_WAIT_SECOND = 3;

    public static final int LOCK_RELEASE_SECOND = 8;

    public static final int REPAY_LOCK_RELEASE_SECOND = 20;

    @Autowired
    private RepayExtraGuaranteePlanRepository feePlanRepository;

    @Autowired
    private RepayGuaranteeFeeService repayGuaranteeFeeService;

    @Autowired
    private UserBankCardRepository userBankCardRepository;

    @Autowired
    private OfflineRepayReduceRepository offlineRepayReduceRepository;

    @Autowired
    private OfflineRepayApplyRepository offlineRepayApplyRepository;

    @Autowired
    private ProjectInfoService projectInfoService;

    @Autowired
    private WarningService warningService;
    @Value("${hxbk.overDueDayAdd}")
    private Integer hxbkOverDueDayAdd;
    /**
     * 默认减免顺序
     */
    private static final List<ReduceItem> DEFAULT_REDUCE_ORDER = Arrays.asList(
        ReduceItem.PENALTY,
        ReduceItem.CONSULT_FEE,
        ReduceItem.GUARANTEE,
        ReduceItem.INTEREST,
        ReduceItem.PRINCIPAL
    );
    /**
     * 拍拍减免顺序
     */
    private static final List<ReduceItem> PPD_REDUCE_ORDER = Arrays.asList(
        ReduceItem.CONSULT_FEE,
        ReduceItem.PENALTY
    );
    private OrderRepository orderRepository;

    /**
     * 线上还款
     */
    public OnlineRepayResponseDto online(OnlineRepayRequestDto request) {
        // 系统维护校验
        if (StringUtil.isNotBlank(repayMaintainStart) && StringUtil.isNotBlank(repayMaintainEnd)) {
            LocalDateTime start = LocalDateTime.parse(repayMaintainStart, DateTimeFormatter.ISO_LOCAL_DATE_TIME);
            LocalDateTime end = LocalDateTime.parse(repayMaintainEnd, DateTimeFormatter.ISO_LOCAL_DATE_TIME);
            if (LocalDateTime.now().isAfter(start) && LocalDateTime.now().isBefore(end)) {
                throw new BizException(repayMaintainMsg, ResultCode.SYS_MAINTAIN);
            }
        }

        // 放款id
        String loanId = request.getLoanId();
        // 获取放款信息
        Loan loan = loanRepository.findById(loanId).orElseThrow(() -> new BizException(ResultCode.BIZ_ERROR));

        OnlineRepayResponseDto response = new OnlineRepayResponseDto();

        logger.info("线上还款请求:{}", JsonUtil.toJsonString(request));
        // 获取期数
        Integer period = request.getPeriod();
        // 结清还是当期
        RepayPurpose repayPurpose = request.getRepayPurpose();

        // 结清处理
        if (RepayPurpose.CLEAR.equals(repayPurpose)) {
            period = getClearPeriod(loanId);
            request.setPeriod(period);
        }

        // 所有还款相关操作互斥
        String lockKey = "cash_business_repay_" + request.getLoanId();
        Locker lock = lockService.getLock(lockKey);

        try {
            boolean locked = lock.tryLock(Duration.ZERO, Duration.ofSeconds(REPAY_LOCK_RELEASE_SECOND));

            if (!locked) {
                throw new BizException(ResultCode.NO_SUBMIT_REPEAT);
            }

            // 费用计划
            RepayExtraGuaranteePlan plan = feePlanRepository.findByLoanIdAndPeriod(loanId, period);
            if (Objects.nonNull(plan)) {
                // 当前期次存在费用扣款计划
                return repayGuaranteeFeeService.feeRepayOnline(plan.getId());
            }

            //当期校验
            if (RepayPurpose.CURRENT.equals(repayPurpose)) {
                this.currentCheck(loan, period);
            } else {
                //结清校验
                this.clearCheck(loan);
            }
            // 校验还款记录
            this.checkRepayRecord(loanId, period);

            // 初始化对客还款记录
            CustomRepayRecord customRepayRecord = initRepayRecord(request, loan);

            // 还款时间段校验
            try {
                trialService.repayDateCheck(loan, repayPurpose);
            } catch (Exception e) {
                logger.error("还款时间段校验异常,loanId:{}", loanId, e);
                customRepayRecord.setRepayState(ProcessState.FAILED);
                customRepayRecord.setFailReason(StringUtils.left("还款时间段校验异常:" + e.getMessage(), BaseConstants.MAX_FAIL_REASON_LENGTH));
                recordRepository.save(customRepayRecord);
                throw e;
            }
            TrialResultVo trialResultVo;
            try {
                // 还款试算
                if (StringUtil.isBlank(request.getRepayDate())) {
                    request.setRepayDate(lvxinSysTimeMockService.isMockTime(LocalDateTime.now()).format(DateTimeFormatter.ofPattern("yyyyMMdd")));
                }
                trialResultVo = trialService.repayTrial(loanId, repayPurpose, period, request.getRepayDate());
                customRepayRecord = updateNormalReapyRecord(customRepayRecord, trialResultVo);
            } catch (Exception e) {
                logger.error("还款试算异常,loanId:{}", loanId, e);

                updateTrialFailReapyRecord(customRepayRecord, e);
                throw e;
            }
            //区分渠道，做减免
            if (FlowChannel.LVXIN.equals(loan.getFlowChannel())) {
                //判断是否逾期还款并且判断是不是在是否可以使用宽限期
                //且宽限期无减免：
                reduction(customRepayRecord, loanId, period, request, trialResultVo, loan);
            } else if (FlowChannel.FQLQY001.equals(loan.getFlowChannel())) {
                //分期乐，无宽限期，无减免
                noreduction(customRepayRecord, request, trialResultVo, loan);
            } else {
                chargeService.chargeMotherAndChild(customRepayRecord, loan);
                BankRepayRecord bankRepayRecord = this.initBankRepayRecord(request, trialResultVo, customRepayRecord.getId(), loan);
                mqService.submitRepay(bankRepayRecord.getId());
            }

            response.setCustomRepayRecordId(customRepayRecord.getId());
            response.setRecordId(customRepayRecord.getId());
            response.setRepayRecordType(RepayRecordType.CUSTOMER_REPAY_RECORD);
            return response;

        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        } finally {
            lock.unlock();
        }
    }

    void reduction(CustomRepayRecord customRepayRecord, String loanId, Integer period, OnlineRepayRequestDto request, TrialResultVo trialResultVo, Loan loan) {
        //判断是否逾期还款并且判断是不是在是否可以使用宽限期
        //且宽限期无减免：
        RepayPlan repayPlan = repayPlanRepository.findByLoanIdAndPeriod(loanId, period);
        LocalDateTime now = lvxinSysTimeMockService.isMockTime(LocalDateTime.now());
        boolean flag = true;
        if (!ObjectUtils.isEmpty(repayPlan)) {
            if (repayPlan.getPlanRepayDate().isBefore(now.toLocalDate())
                && !isGracePeriod(repayPlan, now.toLocalDate(), loan)&&! IsIncludingEquity.Y.equals(loan.getIsIncludingEquity())) {
                //capitalPenalty试算银行罚息
                logger.info("date:{}", repayPlan.getPlanRepayDate().isBefore(now.toLocalDate()));
                flag = onlineExemptionInspection(repayPlan, customRepayRecord, request, trialResultVo.getCapitalPenalty());
            } else if ((request.getPenaltyInterestWaiver().compareTo(BigDecimal.ZERO) > 0 ||
                request.getConsultationFeeWaiver().compareTo(BigDecimal.ZERO) > 0)
                && isGracePeriod(repayPlan, now.toLocalDate(), loan)&& IsIncludingEquity.Y.equals(loan.getIsIncludingEquity())) {
                //未逾期，和宽限期不能有减免
                customRepayRecord.setRepayState(ProcessState.FAILED);
                customRepayRecord.setRemark("减免超限制");
                flag = false;
            }
        }
        BigDecimal big = request.getPenaltyInterestWaiver().add(request.getConsultationFeeWaiver());
        // 计算实还金额
        BigDecimal calActAmount = AmountUtil.sum(customRepayRecord.getTotalAmt());
        logger.info("减免金额:{}，试算金额{}，总金额：{}", big, calActAmount, request.getRepayTotalAmount());
        if (request.getRepayTotalAmount().compareTo(calActAmount) != 0) {
            customRepayRecord.setRepayState(ProcessState.FAILED);
            customRepayRecord.setFailReason("试算金额与参数不一致");
            customRepayRecord = recordRepository.save(customRepayRecord);
            throw new BizException("试算金额与参数不一致", ResultCode.REPAY_OFFLINE_ERROR);
        }
        if (flag) {
            //已经将减免的金额更新到对客还款记录表中，因此后续不需要在处理了
            customRepayRecord = recordRepository.save(customRepayRecord);
            chargeService.chargeMotherAndChild(customRepayRecord, loan);
            BankRepayRecord bankRepayRecord = this.initBankRepayRecord(request, trialResultVo, customRepayRecord.getId(), loan);
            mqService.submitRepay(bankRepayRecord.getId());
        } else {
            BankRepayRecord bankRepayRecord = this.initBankRepayRecord(request, trialResultVo, customRepayRecord.getId(), loan);
            BankRepayRecord bankRepay = bankRepayRecordRepository.findById(bankRepayRecord.getId()).orElseThrow();
            //校验不通过
            logger.info("失败订单入口id:{}", customRepayRecord.getId());
            bankRepay.setFailReason("减免超限制");
            bankRepay.setState(ProcessState.FAILED);
            customRepayRecord.setFailReason("减免超限制");
            customRepayRecord.setRepayState(ProcessState.FAILED);
            bankRepayRecordRepository.save(bankRepay);
            recordRepository.save(customRepayRecord);
            logger.info("失败订单更新完成id:{}", customRepayRecord.getId());
        }
    }

    void noreduction(CustomRepayRecord customRepayRecord, OnlineRepayRequestDto request, TrialResultVo trialResultVo, Loan loan) {
        //获取当期还款计划
        RepayPlan repayPlan = repayPlanRepository.findByLoanIdAndPeriod(loan.getId(), customRepayRecord.getPeriod());

        if (Objects.equals(repayPlan.getCustRepayState(), RepayState.REPAID)) {
            // 当前已还还款失败
            customRepayRecord.setFailReason("当期已还！");
            customRepayRecord.setRepayState(ProcessState.FAILED);
            recordRepository.save(customRepayRecord);
            throw new BizException("当期已还", ResultCode.BIZ_ERROR);
        }

        //分期乐设置外部，补差标识
        customRepayRecord.setOuterFlag(OuterFlag.OUTER);
        customRepayRecord.setWhetherDiff(WhetherDiff.Y);
        customRepayRecord = recordRepository.save(customRepayRecord);
        //无宽限期无减免，透传

        //计算实还金额
        BigDecimal calActAmount = AmountUtil.sum(customRepayRecord.getTotalAmt());
        logger.info("试算金额:{}，还款金额:{}", calActAmount, request.getRepayTotalAmount());
        if (request.getRepayTotalAmount().compareTo(calActAmount) != 0) {
            customRepayRecord.setRepayState(ProcessState.FAILED);
            customRepayRecord.setFailReason("试算金额与还款金额参数不一致");
            customRepayRecord = recordRepository.save(customRepayRecord);
            throw new BizException("试算金额与还款金额参数不一致", ResultCode.REPAY_OFFLINE_ERROR);
        }

        BankRepayRecord bankRepayRecord = this.initBankRepayRecord(request, trialResultVo, customRepayRecord.getId(), loan);
        mqService.submitRepay(bankRepayRecord.getId());
    }

    private boolean onlineExemptionInspection(RepayPlan repayPlan, CustomRepayRecord customRepayRecord, OnlineRepayRequestDto request, BigDecimal capitalPenalty) {
        boolean flag = false;
        //如果减免罚息字段有值，则得保证减免咨询费必须和咨询费额度相同
        //如罚息减免不超过对客罚息-对资罚息
        BigDecimal subtract = repayPlan.getPenaltyAmt().subtract(capitalPenalty);
        //减免金额=减免罚息+减免咨询费
        customRepayRecord.setReduceAmount(request.getPenaltyInterestWaiver().add(request.getConsultationFeeWaiver()));
        //实还罚息=应还罚息-减免罚息
        customRepayRecord.setPenaltyAmt(repayPlan.getPenaltyAmt().subtract(request.getPenaltyInterestWaiver()));
        //实还咨询费=应还咨询费-减免咨询费
        customRepayRecord.setConsultFee(repayPlan.getConsultFee().subtract(request.getConsultationFeeWaiver()));
        //平台罚息=对客-对资-减免罚息
        customRepayRecord.setActPlatformPenaltyAmt(subtract.subtract(request.getPenaltyInterestWaiver()));
        //总金额=试算金额-减免金额
        customRepayRecord.setTotalAmt(customRepayRecord.getTotalAmt().subtract(customRepayRecord.getReduceAmount()));
        if ((request.getPenaltyInterestWaiver().compareTo(BigDecimal.ZERO) > 0) &&
            request.getConsultationFeeWaiver().compareTo(repayPlan.getConsultFee()) == 0 &&
            request.getPenaltyInterestWaiver().compareTo(subtract) <= 0) {
            customRepayRecord.setRemark("减免成功");
            flag = true;
        } else if ((request.getPenaltyInterestWaiver().compareTo(BigDecimal.ZERO) == 0) &&
            request.getConsultationFeeWaiver().compareTo(repayPlan.getConsultFee()) <= 0) {
            customRepayRecord.setRemark("减免成功");
            flag=true;
        }else {
            BigDecimal decimal = customRepayRecord.getActPlatformPenaltyAmt().compareTo(BigDecimal.ZERO) < 0 ? new BigDecimal(0) : customRepayRecord.getActPlatformPenaltyAmt();
            customRepayRecord.setActPlatformPenaltyAmt(decimal);
            //平台罚息=对客-对资-减免罚息
            customRepayRecord.setRepayState(ProcessState.FAILED);
            customRepayRecord.setRemark("减免超限制");
            flag = false;
        }
        return flag;
    }


    /**
     * 现金贷 统一还款请求入口
     *
     * @param request
     * @return
     */
    public String online(OnlineRepayApplyRequest request) {
        OnlineRepayRequestDto requestDto = new OnlineRepayRequestDto();
        requestDto.setLoanId(request.getLoanId());
        requestDto.setPeriod(request.getPeriod());
        requestDto.setRepayPurpose(request.getRepayPurpose());
        requestDto.setOuterRepayNo(request.getOtuRepayNo());
        requestDto.setOperationSource(request.getOperationSource());
        requestDto.setConsultationFeeWaiver(request.getConsultationFeeWaiver());
        requestDto.setPenaltyInterestWaiver(request.getPenaltyInterestWaiver());
        requestDto.setRepayTotalAmount(request.getRepayTotalAmount());
        return online(requestDto).getCustomRepayRecordId();
    }


    private void currentCheck(Loan loan, int period) {
        // 获取最新一期还款
        RepayPlan repayPlan = repayPlanRepository.findByLoanIdAndCustRepayStateOrderByPeriodAsc(loan.getId(), RepayState.NORMAL).get(0);
        if (repayPlan.getPeriod() != period) {
            throw new BizException("请先还第" + repayPlan.getPeriod() + "期", ResultCode.REPAY_PERIOD_ERROR);
        }
        LocalDateTime now = lvxinSysTimeMockService.isMockTime(LocalDateTime.now());
        logger.info("当前时间:{}", now);
        if (now.toLocalDate().isBefore(repayPlan.getPlanRepayDate())) {
            throw new BizException(ResultCode.REPAY_NOT_SUPPORTED_CURRENT);
        }
    }

    //判断是否未逾期，和 不能有减免
    boolean isGracePeriod(RepayPlan repayPlan, LocalDate date, Loan loan) {
        Boolean flag = false;
        //逾期天数
        long overDay = DateUtil.dateDiff(repayPlan.getPlanRepayDate(), date);
        //宽限期
        int graceDay = QhBank.getQhBankBy(loan.getBankChannel()).getGraceDay();
        if (loan.getBankChannel()== BankChannel.HXBK){
            //宽限期 年结期间，湖消的逾期天数会增加
             graceDay =hxbkOverDueDayAdd==null?QhBank.getQhBankBy(loan.getBankChannel()).getGraceDay():QhBank.getQhBankBy(loan.getBankChannel()).getGraceDay()+hxbkOverDueDayAdd;
        }
        logger.info("宽限期:{},逾期天数:{},当前时间:{}", overDay, graceDay, repayPlan.getPlanRepayDate().isAfter(date));
        if (overDay <= graceDay || repayPlan.getPlanRepayDate().isAfter(date)) {
            flag = true;
        }
        return flag;
    }

    /**
     * 线下销账
     */
    public String offline(OfflineRepayApplyRequest request) {
        logger.info("线下销账请求:{}", JsonUtil.toJsonString(request));
        //通过订单id查询放款记录
        Loan loan = loanRepository.findByOrderId(request.getOrderId());
        String loanId = loan.getId();
        RepayPurpose repayPurpose = request.getRepayPurpose();
        //通过放款记录主键查询状态为NORMAL（待还）的还款计划第一条记录的信息，按照期数排序。
        RepayPlan repayPlan = repayPlanRepository.findByLoanIdAndCustRepayStateOrderByPeriodAsc(loanId, RepayState.NORMAL).get(0);

        if (Objects.equals(repayPurpose,RepayPurpose.CLEAR)) {
            request.setPeriod(repayPlan.getPeriod());
        }
        Integer period = request.getPeriod();

        // 查询审批通过待审核的减免
        OfflineRepayReduce repayReduce;
        if (StringUtil.isBlank(request.getReductNo())) {
            repayReduce = offlineRepayReduceRepository
                .findByLoanIdAndPeriodAndRepayPurposeAndAuditStateAndUseState(loanId, period, repayPurpose, AuditStatus.PASS, UseState.WAIT);
        } else {
            //todo: orderId？
            repayReduce = offlineRepayReduceRepository
                .findByOrderIdAndAuditStateAndUseState(request.getReductNo(), AuditStatus.PASS, UseState.WAIT);
        }

        OfflineRepayApply repayApply;
        // 还款试算
        TrialResultVo trialResultVo = trial(loanId, repayPurpose, period, request.getRepayDate());
        if (Objects.nonNull(repayReduce)) {
            repayApply = ManageConvert.INSTANCE.toOfflineRepayApply(repayReduce);
            if (Objects.equals(loan.getFlowChannel(),FlowChannel.PPCJDL)) {
                processOfflineRepayApply(repayApply, PPD_REDUCE_ORDER);
            } else {
                processOfflineRepayApply(repayApply);
            }
            // 更新减免为已使用
            repayReduce.setUseState(UseState.USED);
            offlineRepayReduceRepository.save(repayReduce);
        } else {
            repayApply = initOfflineRepayApply(trialResultVo, request, loan);
        }
        repayApply.setWriteOffType(request.getWriteOffType());
        repayApply.setApplyState(ProcessState.PROCESSING);
        repayApply.setCreatedBy(request.getOperator());
        repayApply.setUpdatedBy(request.getOperator());
        repayApply.setOuterRepayNo(request.getOuterRepayNo());
        if (StringUtils.isNotBlank(request.getRepayDate())) {
            LocalDateTime actTime = request.getRepayDate().length() == 8 ? LocalDate.parse(request.getRepayDate(), DateTimeFormatter.ofPattern("yyyyMMdd")).atStartOfDay() :
                LocalDateTime.parse(request.getRepayDate(), DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
            LocalDate localDate = actTime.toLocalDate();
            repayApply.setActTime(actTime);
            if (StringUtils.isNotBlank(request.getRepayDate()) && !localDate.isEqual(LocalDate.now()) && repayPlan.getPenaltyAmt().compareTo(BigDecimal.ZERO) > 0) {
                logger.info("延迟入账 线下还款时间:{},线下当前系统, ", LocalDate.now());
                repayPlan.setPenaltyAmt(trialResultVo.getPenalty());
                repayPlan = repayPlanRepository.save(repayPlan);
                logger.info("更新还款计划应还金额,{} ", trialResultVo.getPenalty());
            }
        }
        boolean flag = true;
        BigDecimal calActAmount;
        if (Objects.equals(loan.getFlowChannel(),FlowChannel.LVXIN)) {
            BigDecimal big = request.getPenaltyInterestWaiver().add(request.getConsultationFeeWaiver());
            // 计算实还金额
            calActAmount = Objects.nonNull(repayReduce) ? AmountUtil.subtract(repayApply.getAmount(), repayReduce.getReduceAmount())
                : AmountUtil.sum(repayApply.getAmount(), repayApply.getOverflowAmount()).subtract(big);
            logger.info("减免金额:{}，总金额：{}", big, repayApply.getAmount());
            logger.info("calActAmount:{}", calActAmount);
            logger.info("request:{}", JsonUtil.toJsonString(request));
            logger.info("repayApply:{}", JsonUtil.toJsonString(repayApply));
            if (request.getActAmount().compareTo(calActAmount) != 0) {
                throw new BizException("试算金额与参数不一致", ResultCode.REPAY_OFFLINE_ERROR);
            }
            //判断是否逾期还款并且判断是不是在是否可以使用宽限期
            //且宽限期无减免：
            LocalDateTime now = lvxinSysTimeMockService.isMockTime(LocalDateTime.now());
            if (!ObjectUtils.isEmpty(repayPlan)) {
                if (repayPlan.getPlanRepayDate().isBefore(now.toLocalDate()) &&
                    !isGracePeriod(repayPlan, now.toLocalDate(), loan)&& !IsIncludingEquity.Y.equals(loan.getIsIncludingEquity())) {
                    //capitalPenalty试算银行罚息
                    flag = exemptionInspection(repayPlan, repayApply, request, trialResultVo.getCapitalPenalty());
                } else if ((request.getPenaltyInterestWaiver().compareTo(BigDecimal.ZERO) > 0 ||
                    request.getConsultationFeeWaiver().compareTo(BigDecimal.ZERO) > 0)
                    && isGracePeriod(repayPlan, now.toLocalDate(), loan)&& IsIncludingEquity.Y.equals(loan.getIsIncludingEquity())) {
                    //未逾期，和宽限期不能有减免
                    repayApply.setApplyState(ProcessState.FAILED);
                    repayApply.setRemark("减免超限制");
                    flag = false;
                }
            }
            calActAmount = Objects.nonNull(repayReduce) ? AmountUtil.subtract(repayApply.getAmount(), repayReduce.getReduceAmount())
                : AmountUtil.sum(repayApply.getAmount(), repayApply.getOverflowAmount()).subtract(repayApply.getReduceAmount());
            logger.info("减免金额:{}，总金额：{},申请实还总额:{},试算实还总额:{}", repayApply.getReduceAmount(), repayApply.getAmount(), request.getActAmount(), calActAmount);
        } else if (Objects.equals(loan.getFlowChannel(),FlowChannel.FQLQY001)) {
            // 分期乐无减免，无宽限期，透传
            if (repayApply.getActAmount().compareTo(trialResultVo.getAmount()) != 0){
                throw new BizException("还款金额与试算金额不一致", ResultCode.REPAY_OFFLINE_ERROR);
            }
        } else {
            calActAmount = Objects.nonNull(repayReduce) ? AmountUtil.subtract(repayApply.getAmount(), repayReduce.getReduceAmount())
                : AmountUtil.sum(repayApply.getAmount(), repayApply.getOverflowAmount());
            logger.info("减免金额:{}，总金额：{},申请实还总额:{},试算实还总额:{}", repayApply.getReduceAmount(), repayApply.getAmount(), request.getActAmount(), calActAmount);
            if (request.getActAmount().compareTo(calActAmount) != 0) {
                throw new BizException("试算金额与参数不一致", ResultCode.REPAY_OFFLINE_ERROR);
            }
        }

        boolean isCrossDayFlag = false;//是否跨日标志
        String projectCode = loan.getProjectCode();//项目唯一编码
        //线下还款跨日检验
        isCrossDayFlag = checkOfflineRepayCrossDay(request.getRepayDate(),projectCode,repayApply);
        if(isCrossDayFlag){
            //为跨日还款时，设置flag为false，防止调用资方。
            flag = false;
        }

        //保存线下还款申请记录
        repayApply = offlineRepayApplyRepository.save(repayApply);

        // 费用计划
        RepayExtraGuaranteePlan plan = feePlanRepository.findByLoanIdAndPeriod(loanId, period);
        if (Objects.nonNull(plan)) {
            // 当前期次存在费用扣款计划
            if (!Objects.equals(plan.getRepayPurpose(),repayApply.getRepayPurpose())) {
                throw new BizException("repayPurpose不一致", ResultCode.REPAY_OFFLINE_ERROR);
            }
            //额外费用线下销账
            return repayGuaranteeFeeService.feeRepayOffline(plan, repayApply);
        }

        // 初始化还款记录
        BankRepayRecord bankRepayRecord = initOfflineRepayRecord(repayApply, trialResultVo, loan, request.getOuterRepayNo());
        if (flag) {
            //通知core进行线下还款
            mqService.submitRepay(bankRepayRecord.getId());
        } else {
            //如果校验未通过则，不调用资方还款
            BankRepayRecord bankRepay = bankRepayRecordRepository.findById(bankRepayRecord.getId()).orElseThrow();
            CustomRepayRecord customRepayRecord = recordRepository.findById(bankRepayRecord.getSourceRecordId()).orElseThrow();
            //校验不通过
            logger.info("减免失败订单入口id:{}", customRepayRecord.getId());
            bankRepay.setFailReason(repayApply.getRemark());
            customRepayRecord.setFailReason(repayApply.getRemark());
            if(isCrossDayFlag){//还款跨日处理
                bankRepay.setState(ProcessState.PROCESSING);
                customRepayRecord.setRepayState(ProcessState.PROCESSING);
                logger.info("产品要素不支持线下跨日还款时，为跨日还款，不调资方。更新还款记录状态为处理中并进行企微告警，请介入人工处理！");
                warningService.warn("对客还款记录id为：" + customRepayRecord.getId() + "，loan_id为：" + customRepayRecord.getLoanId()
                    + "，项目唯一编码为：" + projectCode + "的产品要素不支持线下跨日还款时，为跨日还款。请介入人工处理！");
            }else{
                logger.info("线下还款减免检验不通过，更新还款记录的状态为失败！");
                bankRepay.setState(ProcessState.FAILED);
                customRepayRecord.setRepayState(ProcessState.FAILED);
            }
            bankRepayRecordRepository.save(bankRepay);
            recordRepository.save(customRepayRecord);
            logger.info("失败订单更新完成id:{}", customRepayRecord.getId());
        }
        return bankRepayRecord.getId();
    }

    /**
     * 线下还款跨日检验
     * @param date 实际转账日期
     * @param projectCode 项目唯一编码
     * @param repayApply 线下还款申请记录
     * @return
     */
    private boolean checkOfflineRepayCrossDay(String date, String projectCode, OfflineRepayApply repayApply) {
        boolean isCrossDayFlag = false;
        if(StringUtils.isBlank(projectCode)){
            logger.error("线下还款时，项目唯一编码为空。请检查！");
            throw new BizException(ResultCode.REPAY_PROJECT_CODE_ISNULL_ERROR);
        }
        //通过项目唯一编码查询对应的产品要素扩展记录
        ProjectElementsExt elementsExt = projectInfoService.queryProjectInfo(projectCode).getElementsExt();
        if(Objects.isNull(elementsExt)){
            logger.error("线下还款时，通过项目唯一编码：[" + projectCode + "]查询产品要素扩展记录为空。请检查！");
            throw new BizException(ResultCode.REPAY_ELEMENTS_EXT_ISNULL_ERROR);
        }
        String allowCrossDayRepay = elementsExt.getAllowCrossDayRepay();//是否支持线下跨日还款
        logger.info("是否支持线下跨日还款的值为：" + allowCrossDayRepay + "，为否,执行跨日还款检验逻辑。为是不走。");
        //判断是否支持线下跨日还款。
        if(Objects.equals(allowCrossDayRepay, ActiveInactive.N.getCode())){//为否,执行跨日还款检验逻辑。
            logger.info("=====产品要素的是否支持线下跨日还款为否,执行跨日还款检验逻辑。========");
            LocalDate repayDate = LocalDate.parse(date, DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
            LocalDateTime now = lvxinSysTimeMockService.isMockTime(LocalDateTime.now());
            if(repayDate.isBefore(now.toLocalDate())){
                logger.info("产品要素不支持线下跨日还款时，为跨日还款。实际转账日期为：" + repayDate + "，账单日为：" + now.toLocalDate());
                repayApply.setApplyState(ProcessState.PROCESSING);//处理中
                repayApply.setRemark("产品要素不支持线下跨日还款时，为跨日还款。实际转账日期为：" + repayDate + "，账单日为：" + now.toLocalDate() + "。请介入人工处理！");
                isCrossDayFlag = true;
            }
        }
        return isCrossDayFlag;
    }

    //检查减免金额是否通过
    //咨询费减免可全额减免，罚息减免不得超过我方收取部分（对客罚息-对资罚息）
    boolean exemptionInspection(RepayPlan repayPlan, OfflineRepayApply apply, OfflineRepayApplyRequest request, BigDecimal capitalPenalty) {
        boolean flag = false;
        //如果减免罚息字段有值，则得保证减免咨询费必须和咨询费额度相同
        //如罚息减免不超过对客罚息-对资罚息
        BigDecimal subtract = repayPlan.getPenaltyAmt().subtract(capitalPenalty);
        //减免金额=减免罚息+减免咨询费
        apply.setReduceAmount(request.getPenaltyInterestWaiver().add(request.getConsultationFeeWaiver()));
        //实还罚息=应还罚息-减免罚息
        apply.setActPenaltyAmt(apply.getPenaltyAmt().subtract(request.getPenaltyInterestWaiver()));
        //实还咨询费=应还咨询费-减免咨询费
        apply.setActConsultFee(apply.getConsultFee().subtract(request.getConsultationFeeWaiver()));
        apply.setActPlatformPenaltyAmt(subtract.subtract(request.getPenaltyInterestWaiver()));
        if ((request.getPenaltyInterestWaiver().compareTo(BigDecimal.ZERO) > 0) &&
            request.getConsultationFeeWaiver().compareTo(repayPlan.getConsultFee()) == 0 &&
            request.getPenaltyInterestWaiver().compareTo(subtract) <= 0
        ) {
            apply.setRemark("减免成功");
            flag = true;
        } else if ((request.getPenaltyInterestWaiver().compareTo(BigDecimal.ZERO) == 0) &&
            request.getConsultationFeeWaiver().compareTo(repayPlan.getConsultFee()) <= 0
        ) {
            apply.setRemark("减免成功");
            flag = true;
        } else {
            BigDecimal decimal = apply.getActPlatformPenaltyAmt().compareTo(BigDecimal.ZERO) < 0 ? new BigDecimal(0) : apply.getActPlatformPenaltyAmt();
            apply.setActPlatformPenaltyAmt(decimal);
            apply.setApplyState(ProcessState.FAILED);
            apply.setRemark("减免超限制");
            flag = false;
        }
        return flag;
    }


    public TrialResultVo trial(String loanId, RepayPurpose repayPurpose, Integer period, String repayDate) {
        if (RepayPurpose.CLEAR == repayPurpose) {
            RepayPlan repayPlan = repayPlanRepository.findByLoanIdAndCustRepayStateOrderByPeriodAsc(loanId, RepayState.NORMAL).get(0);
            period = repayPlan.getPeriod();
        }

        return trialService.repayTrial(loanId, repayPurpose, period, repayDate);
    }


    /**
     * 发起还款
     *
     * @param repayId
     */
    public void repay(String repayId) {
        BankRepayRecord bankRepayRecord = bankRepayRecordRepository.findById(repayId).orElseThrow(() -> new BizException(ResultCode.REPAY_NOT_EXIST));
        CustomRepayRecord customRepayRecord = recordRepository.findById(bankRepayRecord.getSourceRecordId())
            .orElseThrow(() -> new BizException(ResultCode.REPAY_NOT_EXIST));

        if (OuterFlag.OUTER == customRepayRecord.getOuterFlag()) {
            //外部代扣
            outWithholdFlow(bankRepayRecord, customRepayRecord);
        } else {
            withholdFlow(bankRepayRecord,customRepayRecord);
        }
    }

    void withholdFlow(BankRepayRecord bankRepayRecord, CustomRepayRecord customRepayRecord) {
        Loan loan = loanRepository.findById(bankRepayRecord.getLoanId()).orElseThrow(() -> new BizException(ResultCode.BIZ_ERROR));
        // 用户阶段还款银行卡
        UserBankCard userBankCard = platformOnceBoundService.obtainRepayCard(loan);
        WithholdFlow withholdFlow = withholdFlowRepository.findByRepayRecordId(customRepayRecord.getId());

        // 所有还款相关操作互斥
        String lockKey = "cash_business_repay_" + bankRepayRecord.getLoanId();
        Locker lock = lockService.getLock(lockKey);

        try {
            boolean locked = lock.tryLock(Duration.ofSeconds(LOCK_WAIT_SECOND), Duration.ofSeconds(LOCK_RELEASE_SECOND));
            if (!locked) {
                throw new BizException(ResultCode.NO_SUBMIT_REPEAT);
            }

            RepayApplyDto repayApplyDto = new RepayApplyDto();


            this.buildRepayApply(repayApplyDto, bankRepayRecord, loan, userBankCard);
            if (withholdFlow != null) {
                repayApplyDto.setPayOrderNo(withholdFlow.getId());
            }
            if (customRepayRecord.getRepayMode().equals(RepayMode.OFFLINE)) {
                repayApplyDto.setTransferDate(customRepayRecord.getRepayApplyDate().toLocalDate());
            }
            // 更新对客还款记录为处理中
            customRepayRecord.setRepayState(ProcessState.PROCESSING);
            recordRepository.save(customRepayRecord);
            RestResult<RepayResultDto> restResult;
            // 通知资方扣款
            try {
                logger.info("还款申请,core param:{}", JsonUtil.toJsonString(repayApplyDto));
                restResult = finRepayService.repay(repayApplyDto);
                logger.info("还款申请,core返回:{}", JsonUtil.toJsonString(restResult));
            } catch (Exception e) {
                // 异常后也要查询,确保不重复发起
                mqService.submitRepayQueryDelay(bankRepayRecord.getId());
                throw e;
            }

            if (restResult.isSuccess()) {
                // 请求成功
                bankRepayRecord.setState(ProcessState.PROCESSING);
                bankRepayRecord.setBankRepayNo(restResult.getData().getRepayId());
                bankRepayRecordRepository.save(bankRepayRecord);
            }

            mqService.submitRepayQueryDelay(bankRepayRecord.getId());
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        } finally {
            lock.unlock();
        }
    }

    void outWithholdFlow(BankRepayRecord bankRepayRecord, CustomRepayRecord customRepayRecord) {
        logger.info("外部代扣请求,repayId:{}", customRepayRecord.getId());
        Loan loan = loanRepository.findById(bankRepayRecord.getLoanId()).orElseThrow(() -> new BizException(ResultCode.BIZ_ERROR));

        // 用户阶段还款银行卡
        UserBankCard userBankCard = platformOnceBoundService.obtainRepayCard(loan);

        // 所有还款相关操作互斥
        String lockKey = "cash_business_repay_" + bankRepayRecord.getLoanId();
        Locker lock = lockService.getLock(lockKey);

        try {
            boolean locked = lock.tryLock(Duration.ofSeconds(LOCK_WAIT_SECOND), Duration.ofSeconds(LOCK_RELEASE_SECOND));
            if (!locked) {
                throw new BizException(ResultCode.NO_SUBMIT_REPEAT);
            }

            RepayApplyDto repayApplyDto = new RepayApplyDto();
            this.buildRepayApply(repayApplyDto, bankRepayRecord, loan, userBankCard);

            //外部代扣记录
            OutWithholdFlow outWithholdFlow = outWithholdFlowRepository.findByLoanIdAndPeriodAndPayState(loan.getId(),customRepayRecord.getPeriod(), ProcessState.INIT).orElseThrow();
            if (customRepayRecord.getRepayMode().equals(RepayMode.OFFLINE)) {
                outWithholdFlow.setShareInfo(WhetherState.N);
                repayApplyDto.setTransferDate(customRepayRecord.getRepayApplyDate().toLocalDate());
            } else {
                List<OutWithholdShareInfo> outWithholdShareInfos = outWithholdShareInfoRepository.findByOutWithholdFlowId(outWithholdFlow.getId());
                //扣款商户号配置,需要subMerchantId
                outWithholdFlow.setWithholdTypeMemberId(outWithholdShareInfos.get(0).getMerchantNo());
                outWithholdFlow.setShareInfo(WhetherState.N);
                //组装参数
                OutWithholdOutInfo outWithholdOutInfo = outWithholdOutInfoRepository.findByOutWithholdFlowId(outWithholdFlow.getId());
            }
            outWithholdFlow.setRepayRecordId(customRepayRecord.getId());
            outWithholdFlow.setUpdatedBy(customRepayRecord.getUpdatedBy());
            outWithholdFlow.setLoanId(customRepayRecord.getLoanId());
            outWithholdFlow.setPeriod(customRepayRecord.getPeriod());
            outWithholdFlow.setFailReason(customRepayRecord.getFailReason());
            outWithholdFlow.setBizType(ChargeBizType.FINANCE);
            outWithholdFlow.setPayState(ProcessState.PROCESSING);
            //outWithholdFlow.setChannelMchId(merchantConfig.getTerminalId());
            outWithholdFlow.setPayAmount(customRepayRecord.getTotalAmt());
            outWithholdFlow.setPayee(Payee.CAPITAL);
            outWithholdFlow.setChannelId(PaymentChannel.ALLIN_PAY);
            outWithholdFlow.setAgreementNo(userBankCard.getAgreeNo());
            outWithholdFlow = outWithholdFlowRepository.save(outWithholdFlow);
            if (outWithholdFlow != null) {
                repayApplyDto.setPayOrderNo(outWithholdFlow.getId());
            }

            // 更新对客还款记录为处理中
            customRepayRecord.setRepayState(ProcessState.PROCESSING);
            recordRepository.save(customRepayRecord);

            RestResult<RepayResultDto> restResult;
            // 通知资方扣款
            try {
                logger.info("还款申请,core param:{}", JsonUtil.toJsonString(repayApplyDto));
                restResult = finRepayService.repay(repayApplyDto);
                logger.info("还款申请,core返回:{}", JsonUtil.toJsonString(restResult));
            } catch (Exception e) {
                // 异常后也要查询,确保不重复发起
                mqService.submitRepayQueryDelay(bankRepayRecord.getId());
                throw e;
            }

            if (restResult.isSuccess()) {
                // 请求成功
                bankRepayRecord.setState(ProcessState.PROCESSING);
                bankRepayRecord.setBankRepayNo(restResult.getData().getRepayId());
                bankRepayRecordRepository.save(bankRepayRecord);
                // 保存代扣流水号
                outWithholdFlow.setPayOrderNo(restResult.getData().getPayOrderNo());
                outWithholdFlowRepository.save(outWithholdFlow);
            }

            mqService.submitRepayQueryDelay(bankRepayRecord.getId());
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        } finally {
            lock.unlock();
        }
    }


    /**
     * fin-core还款结果
     *
     * @param repayId
     */
    public void repayResult(String repayId) {
        logger.info("还款结果查询入口:{}", repayId);
        BankRepayRecord bankRepayRecord = bankRepayRecordRepository.findById(repayId)
            .orElseThrow(() -> new BizException(ResultCode.REPAY_NOT_EXIST));
        CustomRepayRecord customRepayRecord = recordRepository.findById(bankRepayRecord.getSourceRecordId())
            .orElseThrow(() -> new BizException(ResultCode.REPAY_NOT_EXIST));

        RepayPlan replan = repayPlanRepository.findByLoanIdAndPeriod(bankRepayRecord.getLoanId(), bankRepayRecord.getPeriod());
        if (replan == null || RepayState.REPAID == replan.getCustRepayState()) {
            logger.error("当前还款计划异常 replan:{}", JsonUtil.toJsonString(replan));
            throw new BizException(ResultCode.REPPAY_PLAN_CHECK_ERROR);
        }
        if (OuterFlag.OUTER == customRepayRecord.getOuterFlag()) {
            //外部代扣
            repayResultOutWithholdFlow(repayId, customRepayRecord, bankRepayRecord, replan);
        } else {
            repayResultWithholdFlow(repayId, customRepayRecord, bankRepayRecord, replan);
        }
    }


    void repayResultWithholdFlow(String repayId, CustomRepayRecord customRepayRecord, BankRepayRecord bankRepayRecord, RepayPlan replan) {
        RepayQueryDto repayQueryDto = new RepayQueryDto();
        repayQueryDto.setOuterRepayId(repayId);
        repayQueryDto.setRepayId(bankRepayRecord.getBankRepayNo());
        logger.info("调用资方查询接口repayQueryDto:{}", JsonUtil.toJsonString(repayQueryDto));
        RestResult<RepayResultDto> restResult = finRepayService.queryResult(repayQueryDto);
        logger.info("调用资方查询接口restResult:{}", JsonUtil.toJsonString(restResult));
        RepayResultDto data = restResult.getData();
        if (!restResult.isSuccess() || data == null) {
            throw new BizException(restResult.getMsg(), ResultCode.REPAY_QUERY_ERROR);
        }
        ProcessStatus repayStatus = data.getStatus();

        WithholdFlow withholdFlow = withholdFlowRepository.findByRepayRecordId(customRepayRecord.getId());
        if (withholdFlow != null) {
            String withholdFlowStatus = withholdFlow.getPayState().name();
            //宝付、长银状态不一致预警
            if (!withholdFlowStatus.startsWith(repayStatus.name().substring(0, 4))
                && (ProcessStatus.FAIL.equals(repayStatus) || ProcessStatus.SUCCESS.equals(repayStatus))) {
                // 计算时间差（绝对值）
                long minutes = Duration.between(withholdFlow.getCreatedTime(), data.getActRepayTime()).abs().toMinutes();
                Integer alarmNum = (int) (minutes / Integer.parseInt(baofuAlarmInterval));
                // 判断是否大于30分钟
                if (alarmNum >= 1 && cacheService.get(withholdFlow.getId()) == null) {
                    cacheService.put(withholdFlow.getId(), LocalDateTime.now(), Duration.ofMinutes(Long.parseLong(baofuAlarmInterval)));
                    warningService.warn(repayId + "还款结果查询：宝付、资方状态不一致,宝付状态：" + withholdFlowStatus + "，资方状态：" + repayStatus.name());
                }
                mqService.submitRepayQueryDelay(repayId);
                return;
            }
        }
        if (ProcessStatus.FAIL.equals(repayStatus)) {
            failUpdate(bankRepayRecord, customRepayRecord, restResult);
        } else if (ProcessStatus.SUCCESS.equals(repayStatus)) {
            capitalSuccessUpdate(bankRepayRecord, restResult, replan, customRepayRecord);
            if(withholdFlow != null&&cacheService.get(withholdFlow.getId())!= null) {
                //移除告警redis
                cacheService.delete(withholdFlow.getId());
            }
        } else {
            mqService.submitRepayQueryDelay(repayId);
        }
    }


    void repayResultOutWithholdFlow(String repayId, CustomRepayRecord customRepayRecord, BankRepayRecord bankRepayRecord, RepayPlan replan) {
        RepayQueryDto repayQueryDto = new RepayQueryDto();
        repayQueryDto.setOuterRepayId(repayId);
        repayQueryDto.setRepayId(bankRepayRecord.getBankRepayNo());
        logger.info("调用资方查询接口repayQueryDto:{}", JsonUtil.toJsonString(repayQueryDto));
        RestResult<RepayResultDto> restResult = finRepayService.queryResult(repayQueryDto);
        logger.info("调用资方查询接口restResult:{}", JsonUtil.toJsonString(restResult));
        RepayResultDto data = restResult.getData();
        if (!restResult.isSuccess() || data == null) {
            throw new BizException(restResult.getMsg(), ResultCode.REPAY_QUERY_ERROR);
        }
        ProcessStatus repayStatus = data.getStatus();

        OutWithholdFlow outWithholdFlow = outWithholdFlowRepository.findByRepayRecordId(customRepayRecord.getId());
        if (outWithholdFlow.getPayState().isFinal()) {
            logger.error("外部扣款结果已是终态, 跳过查询:{}", outWithholdFlow.getId());
        }
        if (ProcessStatus.FAIL.equals(repayStatus)) {
            outWithholdFlow.setPayState(ProcessState.FAILED);
            outWithholdFlow.setFailReason(data.getFailMsg());
            outWithholdFlowRepository.save(outWithholdFlow);
            failUpdate(bankRepayRecord, customRepayRecord, restResult);
        } else if (ProcessStatus.SUCCESS.equals(repayStatus)) {
            outWithholdFlow.setPayState(ProcessState.SUCCEED);
            outWithholdFlow.setPayOrderNo(data.getPayOrderNo());
            outWithholdFlow.setPayTime(LocalDateTime.now());
            outWithholdFlowRepository.save(outWithholdFlow);
            capitalSuccessUpdate(bankRepayRecord, restResult, replan, customRepayRecord);
        } else {
            mqService.submitRepayQueryDelay(repayId);
        }
    }


    private void failUpdate(BankRepayRecord bankRepayRecord, CustomRepayRecord customRepayRecord, RestResult<RepayResultDto> restResult) {
        logger.info("失败订单入口id:{}", customRepayRecord.getId());
        bankRepayRecord.setFailReason(restResult.getData().getFailMsg());
        bankRepayRecord.setState(ProcessState.FAILED);
        customRepayRecord.setFailReason(restResult.getData().getFailMsg());
        customRepayRecord.setRepayState(ProcessState.FAILED);
        bankRepayRecordRepository.save(bankRepayRecord);
        recordRepository.save(customRepayRecord);
        logger.info("失败订单更新完成id:{}", customRepayRecord.getId());

        Loan loan = loanRepository.findById(bankRepayRecord.getLoanId()).orElseThrow();
        UserInfo userInfo = userInfoRepository.findById(loan.getUserId()).orElseThrow();

        // 提前结清操作失败发送短信
        if (FlowChannel.isRepaySendSms(loan.getFlowChannel()) && RepayMode.ONLINE == bankRepayRecord.getRepayMode()
            && RepayPurpose.CLEAR == bankRepayRecord.getRepayPurpose()) {
//            smsService.send(SmsTemplate.REPAY_CLEAR_FAILED, Map.of("name", userInfo.getName(), "flowChannel", loan.getFlowChannel().name()),
//                    userInfo.getMobile());
        }

        if (RepayMode.OFFLINE == bankRepayRecord.getRepayMode()) {
            OfflineRepayApply repayApply = offlineRepayApplyRepository.findByOuterRepayNo(customRepayRecord.getOuterRepayNo());
            Optional.ofNullable(repayApply).orElseThrow(() -> new BizException(ResultCode.REPAY_NOT_EXIST));
            repayApply.setApplyState(ProcessState.FAILED);
            repayApply.setRemark(restResult.getData().getFailMsg());
            offlineRepayApplyRepository.save(repayApply);
        }

        // 还款回调
        CallBackDTO callBackDTO = new CallBackDTO();
        callBackDTO.setFlowChannel(loan.getFlowChannel());
        callBackDTO.setCallbackState(CallbackState.REPAY_FAIL);
        callBackDTO.setBusinessId(customRepayRecord.getId());
        mqService.submitCallbackCommonNotify(JsonUtil.toJsonString(callBackDTO));

        // 催收消息发送
        // mqService.submitPlatRepay(customRepayRecord.getId());


    }

    private void capitalSuccessUpdate(BankRepayRecord bankRepayRecord, RestResult<RepayResultDto> restResult,
                                      RepayPlan replan, CustomRepayRecord customRepayRecord) {
        logger.info("对资成功订单入口id:{}", bankRepayRecord.getId());
        bankRepayRecord.setState(ProcessState.SUCCEED);
        bankRepayRecord.setRepayTime(restResult.getData().getActRepayTime());
        bankRepayRecord.setBankRepayNo(restResult.getData().getRepayId());
        replan.setActPrincipalAmt(customRepayRecord.getPrincipalAmt());
        replan.setActGuaranteeAmt(customRepayRecord.getGuaranteeAmt());
        replan.setActPenaltyAmt(customRepayRecord.getPenaltyAmt());
        //replan.setActPenaltyAmt(customRepayRecord.getCapitalPenaltyAmt().add(customRepayRecord.getActPlatformPenaltyAmt()));
        replan.setActCapitalPenaltyAmt(customRepayRecord.getCapitalPenaltyAmt());
        replan.setActBreachAmt(customRepayRecord.getBreachAmt());
        replan.setActAmount(customRepayRecord.getTotalAmt());
        replan.setActInterestAmt(customRepayRecord.getInterestAmt());
        replan.setActRepayTime(restResult.getData().getActRepayTime());
        replan.setActConsultFee(customRepayRecord.getConsultFee());
        Loan loan = loanRepository.findById(bankRepayRecord.getLoanId()).orElseThrow();
        //24权益应还罚息
        if(IsIncludingEquity.Y.equals(loan.getIsIncludingEquity())) {
            replan.setPenaltyAmt(customRepayRecord.getPenaltyAmt());
        }
        //
        bankRepayRecordRepository.save(bankRepayRecord);
        replan = repayPlanRepository.save(replan);
        customRepayRecord.setRepaidDate(restResult.getData().getActRepayTime());
        customRepayRecord = recordRepository.save(customRepayRecord);
        //
        if (RepayMode.ONLINE == bankRepayRecord.getRepayMode()) {
            // 线上
            eventPublisher.publishEvent(new PrincipalRepaySucceedEvent(customRepayRecord.getId()));
        } else {
            // 线下
            logger.info("线下还款成功,id:{}", bankRepayRecord.getId());
            replan.setActPrincipalAmt(customRepayRecord.getPrincipalAmt());
            replan.setActInterestAmt(customRepayRecord.getInterestAmt());
            replan.setActGuaranteeAmt(customRepayRecord.getGuaranteeAmt());
            replan.setActPenaltyAmt(customRepayRecord.getPenaltyAmt());
            //replan.setActPenaltyAmt(customRepayRecord.getCapitalPenaltyAmt().add(customRepayRecord.getActPlatformPenaltyAmt()));
            replan.setActBreachAmt(customRepayRecord.getBreachAmt());
            replan.setActConsultFee(customRepayRecord.getConsultFee());
            replan.setActAmount(AmountUtil.sum(replan.getActPrincipalAmt(), replan.getActInterestAmt(), replan.getActGuaranteeAmt(),
                replan.getActPenaltyAmt(), replan.getActBreachAmt(), replan.getActConsultFee()));
            repayPlanRepository.save(replan);
            updateSuccessOfflineRecordAndPlan(customRepayRecord);

            eventPublisher.publishEvent(new RepaySucceedResultEvent(customRepayRecord.getId()));
        }

        // loanId抛送mq
        logger.info("抛送loanId至mq,loanId:{}", bankRepayRecord.getLoanId());
        //mqService.submitShmPush(bankRepayRecord.getLoanId());
    }

    /**
     * 还款通知core
     *
     * @param
     */
    public void notifyCore(String repayRecordId) {
        // 还款记录
        CustomRepayRecord customRepayRecord = recordRepository.findById(repayRecordId).orElseThrow();
        if (customRepayRecord.getCapitalGuaranteeAmt() == null) {
            throw new RuntimeException("还款通知core 对资应还融担费为null，请人工处理");
        }
        Loan loan = loanRepository.findById(customRepayRecord.getLoanId()).orElseThrow();
        // 对资还款记录
        BankRepayRecord bankRepayRecord = RepayConvert.INSTANCE.toBankRepayRecord(customRepayRecord);
        // bankRepayRecord.setPenalty(BigDecimal.ZERO);
        // 计算总金额
        bankRepayRecord.setAmount(AmountUtil.sum(
            bankRepayRecord.getPrincipal(),
            bankRepayRecord.getInterest(),
            bankRepayRecord.getPenalty(),
            bankRepayRecord.getPlatformPenalty(),
            bankRepayRecord.getGuarantee(),
            bankRepayRecord.getConsultFee(),
            bankRepayRecord.getBreach()
        ));
        bankRepayRecord.setRepayType(RepayType.REPAY);
        bankRepayRecord.setState(ProcessState.PROCESSING);
        bankRepayRecordRepository.save(bankRepayRecord);

        RepayResultDto resultDto = remoteRepayNotify(bankRepayRecord);
        logger.info("还款通知core响应:{},{}", repayRecordId, JSON.toJSONString(resultDto));
    }

    private RepayResultDto remoteRepayNotify(BankRepayRecord bankRepayRecord) {
        Loan loan = loanRepository.findById(bankRepayRecord.getLoanId()).orElseThrow(() -> new BizException(ResultCode.LOAN_NOT_EXIST));

        //
        RepayApplyDto repayApply = RepayConvert.INSTANCE.toRepayApplyDto(bankRepayRecord);


        repayApply.setLoanId(loan.getLoanNo());

        logger.info("通知core还款,{}", JSON.toJSONString(repayApply));
        RestResult<RepayResultDto> result = finRepayService.repay(repayApply);
        logger.info("通知core还款返回,{}", JSON.toJSONString(result));
        //
        processCoreResult(bankRepayRecord, result);
        return result.getData();
    }


    /**
     * 还款通知core结果查询
     *
     * @param repayRecordId
     */
    public void notifyCoreResult(String repayRecordId) {
        BankRepayRecord bankRepayRecord = bankRepayRecordRepository.findById(repayRecordId).orElseThrow();
        //
        RepayQueryDto queryDto = new RepayQueryDto();
        queryDto.setOuterRepayId(bankRepayRecord.getId());
        RestResult<RepayResultDto> result = finRepayService.queryResult(queryDto);
        processCoreResult(bankRepayRecord, result);
    }

    public void processCoreResult(BankRepayRecord bankRepayRecord, RestResult<RepayResultDto> result) {
        if (!ResultCode.SUCCESS.getCode().equals(result.getCode()) || Objects.isNull(result.getData()) || ProcessStatus.FAIL == result.getData().getStatus()) {
            String failMsg = Optional.ofNullable(result.getData()).map(RepayResultDto::getFailMsg).orElse(result.getMsg());

            bankRepayRecord.setState(ProcessState.FAILED);
            bankRepayRecord.setRemark(failMsg);
            bankRepayRecordRepository.save(bankRepayRecord);
            throw new BizException(failMsg, ResultCode.BIZ_ERROR);
        }

        if (ProcessStatus.SUCCESS.equals(result.getData().getStatus())) {
            // 更新对资还款
            bankRepayRecord.setState(ProcessState.SUCCEED);
            bankRepayRecord.setRepayTime(Optional.ofNullable(result.getData().getActRepayTime()).orElse(LocalDateTime.now()));
            bankRepayRecord.setBankRepayNo(result.getData().getRepayId());
            bankRepayRecordRepository.save(bankRepayRecord);
        }

        if (ProcessStatus.PROCESSING.equals(result.getData().getStatus())) {
            // 处理中, 需异步查询core结果
            mqService.submitRepayNotifyResultDelay(bankRepayRecord.getId());
        }
    }


    /**
     * 构建还款申请参数dto
     *
     * @param repayApplyDto
     * @param bankRepayRecord
     * @param loan
     * @param userBankCard
     */
    private void buildRepayApply(RepayApplyDto repayApplyDto, BankRepayRecord bankRepayRecord, Loan loan, UserBankCard userBankCard) {
        repayApplyDto.setOuterLoanId(Optional.ofNullable(loan.getLoanRecordId()).orElse(loan.getId()));
        repayApplyDto.setLoanId(loan.getLoanNo());
        repayApplyDto.setOuterRepayId(bankRepayRecord.getId());
        repayApplyDto.setAmount(bankRepayRecord.getAmount());
        repayApplyDto.setPeriod(bankRepayRecord.getPeriod());
        repayApplyDto.setRepayPurpose(com.jinghang.capital.api.dto.repay.RepayPurpose.valueOf(bankRepayRecord.getRepayPurpose().name()));
        repayApplyDto.setRepayMode(bankRepayRecord.getRepayMode() == RepayMode.ONLINE ? com.jinghang.capital.api.dto.repay.RepayMode.ONLINE
            : com.jinghang.capital.api.dto.repay.RepayMode.OFFLINE);

        if (bankRepayRecord.getRepayMode() == RepayMode.OFFLINE && bankRepayRecord.getRepayTime() != null) {
            repayApplyDto.setTransferDate(bankRepayRecord.getRepayTime().toLocalDate());
        }
        repayApplyDto.setRepayType(com.jinghang.capital.api.dto.repay.RepayType.REPAY);
        repayApplyDto.setPrincipal(bankRepayRecord.getPrincipal());
        repayApplyDto.setInterest(bankRepayRecord.getInterest());
        repayApplyDto.setGuaranteeFee(bankRepayRecord.getGuarantee());
        repayApplyDto.setOverdueFee(bankRepayRecord.getPenalty());
        repayApplyDto.setPlatformOverdueFee(bankRepayRecord.getPlatformPenalty());
        repayApplyDto.setConsultFee(bankRepayRecord.getConsultFee());
        repayApplyDto.setBreachFee(bankRepayRecord.getBreach());
        // 绑卡协议号
        repayApplyDto.setAgreementNo(userBankCard.getAgreeNo());
        if (Objects.nonNull(userBankCard)) {
            repayApplyDto.setRepayRelUser(userBankCard.getCardName());
            repayApplyDto.setRepayBankCode(userBankCard.getBankCode());
            repayApplyDto.setAgreementNo(userBankCard.getAgreeNo());
            repayApplyDto.setRepayAcctNo(userBankCard.getCardNo());
            repayApplyDto.setRepayRelCard(userBankCard.getCertNo());
            repayApplyDto.setRepayRelPhone(userBankCard.getPhone());
            repayApplyDto.setRepayBankName(userBankCard.getBankName());
            ProtocolChannel protocolChannel = EnumConvert.INSTANCE.toCoreApi(userBankCard.getChannel());
            if (Objects.nonNull(protocolChannel)) {
                repayApplyDto.setChargeChannelId(protocolChannel.name());
            }
        }
    }

    /**
     * 校验处理中还款记录
     *
     * @param loanId
     * @param period
     */
    private void checkRepayRecord(String loanId, Integer period) {
        // 对客的还款记录
        List<CustomRepayRecord> byLoanIdAndPeriod = recordRepository.findAllByLoanIdAndPeriod(loanId, period);
        if (!CollectionUtils.isEmpty(byLoanIdAndPeriod)) {
            if (byLoanIdAndPeriod.stream().anyMatch(customRepayRecord ->
                StringUtils.equalsAny(customRepayRecord.getRepayState().name(),
                    ProcessState.SUCCEED.name(), ProcessState.INIT.name(), ProcessState.PROCESSING.name()))) {
                logger.error("当前订单正在处理中 loanId={} period={}", loanId, period);
                throw new BizException(ResultCode.REPAY_CHECK_ERROR);
            }
        }
        // 对资的还款记录
        List<BankRepayRecord> bankRepayRecords = bankRepayRecordRepository.findAllByLoanIdAndPeriod(loanId, period);
        if (!CollectionUtils.isEmpty(bankRepayRecords)) {
            if (byLoanIdAndPeriod.stream().anyMatch(
                bankRepayRecord -> StringUtils.equalsAny(
                    bankRepayRecord.getRepayState().name(), ProcessState.INIT.name(), ProcessState.SUCCEED.name(),
                    ProcessState.PROCESSING.name()))) {
                logger.error("当前订单正在处理中 loanId={} period={}", loanId, period);
                throw new BizException(ResultCode.REPAY_CHECK_ERROR);
            }
        }
    }

    /**
     * 初始化对客还款记录
     *
     * @param request 还款请求
     * @param loan    借据
     * @return
     */
    private CustomRepayRecord initRepayRecord(OnlineRepayRequestDto request, Loan loan) {
        CustomRepayRecord customRepayRecord = new CustomRepayRecord();
        customRepayRecord.setLoanId(loan.getId());
        customRepayRecord.setPeriod(request.getPeriod());
        customRepayRecord.setRepayApplyDate(LocalDateTime.now());
        customRepayRecord.setRepayPurpose(request.getRepayPurpose());
        customRepayRecord.setRepayMode(RepayMode.ONLINE);
        customRepayRecord.setRepayState(ProcessState.INIT);
        customRepayRecord.setNeedTwiceState(WhetherState.N);
        customRepayRecord.setPaySide(PaySide.CAPITAL);
        customRepayRecord.setOperationSource(request.getOperationSource());
        customRepayRecord.setOuterRepayNo(request.getOuterRepayNo());
        customRepayRecord.setProjectCode(loan.getProjectCode());//项目唯一编码
        // 对客还款记录
        CustomRepayRecord record = recordRepository.save(customRepayRecord);
        logger.info("对客还款记录id:{}", record.getId());
        return record;
    }


    /**
     *
     */
    private CustomRepayRecord updateNormalReapyRecord(CustomRepayRecord customRepayRecord, TrialResultVo trialResultVo) {
        customRepayRecord.setPrincipalAmt(trialResultVo.getPrincipal());
        customRepayRecord.setInterestAmt(trialResultVo.getInterest());
        customRepayRecord.setBreachAmt(Objects.isNull(trialResultVo.getBreachFee()) ? BigDecimal.ZERO : trialResultVo.getBreachFee());
        customRepayRecord.setGuaranteeAmt(trialResultVo.getGuaranteeFee());
        customRepayRecord.setCapitalGuaranteeAmt(trialResultVo.getCapitalGuaranteeFee());
        customRepayRecord.setExtraGuaranteeAmt((trialResultVo.getGuaranteeFee() == null ? BigDecimal.ZERO : trialResultVo.getGuaranteeFee()).subtract(trialResultVo.getCapitalGuaranteeFee()));
        customRepayRecord.setConsultFee(trialResultVo.getConsultFee());
        customRepayRecord.setPenaltyAmt(trialResultVo.getPenalty());
        customRepayRecord.setCapitalPenaltyAmt(trialResultVo.getCapitalPenalty());
        customRepayRecord.setTotalAmt(trialResultVo.getAmount());
        customRepayRecord.setReduceAmount(new BigDecimal(0));
        return recordRepository.save(customRepayRecord);
    }


    /**
     * 试算异常，对客还款记录置为失败
     */
    private CustomRepayRecord updateTrialFailReapyRecord(CustomRepayRecord customRepayRecord, Exception e) {
        customRepayRecord.setRepayState(ProcessState.FAILED);
        customRepayRecord.setFailReason(StringUtils.left("试算异常:" + e.getMessage(), BaseConstants.MAX_FAIL_REASON_LENGTH));
        return recordRepository.save(customRepayRecord);
    }


    /**
     * 初始化对资还款记录
     *
     * @param request
     * @param trialResultVo
     * @param customRepayRecordId
     * @param loan
     * @return
     */
    private BankRepayRecord initBankRepayRecord(OnlineRepayRequestDto request, TrialResultVo trialResultVo, String customRepayRecordId, Loan loan) {
        BankRepayRecord bankRepayRecord = new BankRepayRecord();
        bankRepayRecord.setProjectCode(loan.getProjectCode());//项目唯一编码
        bankRepayRecord.setSourceRecordId(customRepayRecordId);
        bankRepayRecord.setLoanId(loan.getId());
        bankRepayRecord.setPeriod(request.getPeriod());
        bankRepayRecord.setPrincipal(trialResultVo.getPrincipal());
        bankRepayRecord.setInterest(trialResultVo.getInterest());
        bankRepayRecord.setGuarantee(trialResultVo.getCapitalGuaranteeFee());
        //资方罚息
        bankRepayRecord.setPenalty(trialResultVo.getCapitalPenalty());

        //平台违约金
        bankRepayRecord.setBreach(BigDecimal.ZERO);
        bankRepayRecord.setRepayPurpose(request.getRepayPurpose());
        // 线上
        bankRepayRecord.setRepayMode(RepayMode.ONLINE);
        // 还款
        bankRepayRecord.setRepayType(RepayType.REPAY);
        // 还款状态初始化
        bankRepayRecord.setState(ProcessState.INIT);
        // 还款总额
        bankRepayRecord.setAmount(AmountUtil.sum(
            bankRepayRecord.getPrincipal(),
            bankRepayRecord.getInterest(),
            bankRepayRecord.getPenalty(),
            bankRepayRecord.getPlatformPenalty(),
            bankRepayRecord.getGuarantee(),
            bankRepayRecord.getConsultFee()
        ));

        bankRepayRecord = bankRepayRecordRepository.save(bankRepayRecord);
        logger.info("对资还款记录id:{}", bankRepayRecord.getId());
        return bankRepayRecord;
    }

    /**
     * 查询某天还款成功的记录
     *
     * @param reccDate 还款日前
     * @return
     */
    public List<CustomRepayRecord> findSuccessCustomRepayRecord(LocalDate reccDate) {
        LocalDateTime startDay = reccDate.atStartOfDay();
        LocalDateTime endDay = reccDate.plusDays(1L).atStartOfDay();
        return recordRepository.findByRepayStateAndRepaidDateBetween(ProcessState.SUCCEED, startDay, endDay);
    }

    // private boolean checkRepayTime() {
    //     SimpleDateFormat df = new SimpleDateFormat("HH:mm");
    //     Date now;
    //     Date beginTime;
    //     Date endTime;
    //     String[] split = repayTime.split("-");
    //     try {
    //         now = df.parse(df.format(new Date()));
    //         beginTime = df.parse(split[0]);
    //         endTime = df.parse(split[1]);
    //
    //         Calendar date = Calendar.getInstance();
    //         date.setTime(now);
    //         Calendar begin = Calendar.getInstance();
    //         begin.setTime(beginTime);
    //         Calendar end = Calendar.getInstance();
    //         end.setTime(endTime);
    //         return date.after(begin) && date.before(end);
    //     } catch (Exception e) {
    //         logger.error("时间处理异常{}", e.getMessage());
    //         return false;
    //     }
    // }

    private Integer getClearPeriod(String loanId) {
        // 获取最新一期还款
        RepayPlan repayPlan = repayPlanRepository.findByLoanIdAndCustRepayStateOrderByPeriodAsc(loanId, RepayState.NORMAL).get(0);
        // 获取当前期数
        return repayPlan.getPeriod();
    }

    /**
     * 结清处理
     *
     * @param loan
     * @return
     */
    private Integer clearCheck(Loan loan) {
        LocalDateTime now = lvxinSysTimeMockService.isMockTime(LocalDateTime.now());
        // 获取最新一期还款
        RepayPlan repayPlan = repayPlanRepository.findByLoanIdAndCustRepayStateOrderByPeriodAsc(loan.getId(), RepayState.NORMAL).get(0);
        // 获取当前期数
        Integer period = repayPlan.getPeriod();

        // 还款日当天不允许结清
        /*if (repayPlan.getPlanRepayDate().isEqual(now.toLocalDate())) {
            throw new BizException(ResultCode.REPAY_CLEAR_NOT_SUPPORTED_REPAY_DATE);
        }*/
        logger.info("还款计划表的主键id为：{}，计划还款日为：{},当前系统日期为：{}", repayPlan.getId(), repayPlan.getPlanRepayDate(), now.toLocalDate());
        // 逾期不允许结清
        if (repayPlan.getPlanRepayDate().isBefore(now.toLocalDate())) {
            throw new BizException(ResultCode.REPAY_CLEAR_NOT_SUPPORTED_OVERDUE);
        }

        // 有当期费用未扣到, 不允许结清
        if (feePlanRepository.existsByLoanIdAndPeriodAndRepayPurposeAndPlanState(loan.getId(), period, RepayPurpose.CURRENT, RepayState.NORMAL)) {
            throw new BizException(ResultCode.REPAY_CLEAR_NOT_SUPPORTED);
        }

        return period;

    }

    private OfflineRepayApply initOfflineRepayApply(TrialResultVo trialResultVo, OfflineRepayApplyRequest request, Loan loan) {
        OfflineRepayApply apply = ManageConvert.INSTANCE.toOfflineRepayApply(trialResultVo);
        apply.setOrderId(request.getOrderId());
        apply.setLoanId(loan.getId());
        apply.setPeriod(request.getPeriod());
        apply.setRepayPurpose(request.getRepayPurpose());
        apply.setActAmount(AmountUtil.safeAmount(request.getActAmount()));
        apply.setOverflowAmount(AmountUtil.safeAmount(request.getOverflowAmount()));
        apply.setBreachFee(trialResultVo.getBreachFee());
        processOfflineRepayApply(apply);
        return apply;
    }

    public void processOfflineRepayApply(OfflineRepayApply apply) {
        processOfflineRepayApply(apply, DEFAULT_REDUCE_ORDER);
    }

    public void processOfflineRepayApply(OfflineRepayApply apply, List<ReduceItem> reduceOrder) {
        BigDecimal reduceAmount = AmountUtil.safeAmount(apply.getReduceAmount());
        if (reduceAmount.compareTo(BigDecimal.ZERO) == 0) {
            return;
        }
        // 初始化实际金额
        apply.setOverflowAmount(BigDecimal.ZERO);
        initializeActualAmounts(apply);

        // 按照指定顺序进行减免
        for (ReduceItem item : reduceOrder) {
            if (reduceAmount.compareTo(BigDecimal.ZERO) <= 0) {
                break;
            }
            BigDecimal currentAmount = AmountUtil.safeAmount(item.getGetter().apply(apply));
            if (currentAmount.compareTo(BigDecimal.ZERO) > 0) {
                if (reduceAmount.compareTo(currentAmount) <= 0) {
                    item.getSetter().accept(apply, currentAmount.subtract(reduceAmount));
                    reduceAmount = BigDecimal.ZERO;
                } else {
                    item.getSetter().accept(apply, BigDecimal.ZERO);
                    reduceAmount = reduceAmount.subtract(currentAmount);
                }
            }
        }

        if (reduceAmount.compareTo(BigDecimal.ZERO) > 0) {
            throw new BizException("线下还款减免计算后仍有剩余减免金额:" + reduceAmount.toPlainString(),
                ResultCode.REPAY_OFFLINE_REDUCE_ERROR);
        }
    }

    private void initializeActualAmounts(OfflineRepayApply apply) {
        apply.setActConsultFee(AmountUtil.safeAmount(apply.getConsultFee()));
        apply.setActPenaltyAmt(AmountUtil.safeAmount(apply.getPenaltyAmt()));
        apply.setActGuaranteeAmt(AmountUtil.safeAmount(apply.getGuaranteeAmt()));
        apply.setActInterestAmt(AmountUtil.safeAmount(apply.getInterestAmt()));
        apply.setActPrincipalAmt(AmountUtil.safeAmount(apply.getPrincipalAmt()));
    }

    private BankRepayRecord initOfflineRepayRecord(OfflineRepayApply repayApply, TrialResultVo trialResultVo, Loan loan, String outerRepayNo) {
        // 对客
        CustomRepayRecord customRepayRecord = ManageConvert.INSTANCE.toCustomRepayRecord(repayApply);
        customRepayRecord.setTotalAmt(AmountUtil.subtract(repayApply.getActAmount(), repayApply.getOverflowAmount()));

        //线下还款融担费和资方没关系,记为0
        customRepayRecord.setCapitalGuaranteeAmt(BigDecimal.ZERO);
        customRepayRecord.setExtraGuaranteeAmt(BigDecimal.ZERO);
        //资方罚息
        customRepayRecord.setCapitalPenaltyAmt(trialResultVo.getCapitalPenalty());
        customRepayRecord.setNeedTwiceState(WhetherState.N);
        customRepayRecord.setOuterRepayNo(outerRepayNo);
        //减免金额
        customRepayRecord.setReduceAmount(repayApply.getReduceAmount());
        customRepayRecord.setActPlatformPenaltyAmt(repayApply.getActPlatformPenaltyAmt());
        customRepayRecord.setRepayApplyDate(repayApply.getActTime());
        if (FlowChannel.FQLQY001.equals(loan.getFlowChannel())) {
            customRepayRecord.setOuterFlag(OuterFlag.OUTER);
            customRepayRecord.setWhetherDiff(WhetherDiff.N);
        }
        customRepayRecord.setProjectCode(loan.getProjectCode());//项目唯一编码
        customRepayRecord = recordRepository.save(customRepayRecord);

        // 对资
        BankRepayRecord bankRepayRecord = new BankRepayRecord();
        bankRepayRecord.setProjectCode(loan.getProjectCode());//项目唯一编码
        bankRepayRecord.setSourceRecordId(customRepayRecord.getId());
        bankRepayRecord.setLoanId(customRepayRecord.getLoanId());
        bankRepayRecord.setPeriod(customRepayRecord.getPeriod());
        bankRepayRecord.setPrincipal(trialResultVo.getPrincipal());
        bankRepayRecord.setInterest(trialResultVo.getInterest());
        bankRepayRecord.setGuarantee(trialResultVo.getCapitalGuaranteeFee());
        bankRepayRecord.setPenalty(trialResultVo.getCapitalPenalty());

        bankRepayRecord.setBreach(BigDecimal.ZERO);
        bankRepayRecord.setRepayPurpose(customRepayRecord.getRepayPurpose());
        bankRepayRecord.setRepayMode(RepayMode.OFFLINE);
        bankRepayRecord.setRepayType(RepayType.REPAY);
        bankRepayRecord.setState(ProcessState.INIT);
        bankRepayRecord.setAmount(AmountUtil.sum(
            bankRepayRecord.getPrincipal(),
            bankRepayRecord.getInterest(),
            bankRepayRecord.getPenalty(),
            bankRepayRecord.getPlatformPenalty(),
            bankRepayRecord.getGuarantee(),
            bankRepayRecord.getConsultFee()
        ));
        bankRepayRecordRepository.save(bankRepayRecord);

        return bankRepayRecord;
    }

    /**
     * 更新还款计划&还款计划
     */
    private void updateSuccessOfflineRecordAndPlan(CustomRepayRecord repayRecord) {
        repayRecord.setRepayState(ProcessState.SUCCEED);
        repayRecord = recordRepository.save(repayRecord);

        OfflineRepayApply repayApply = offlineRepayApplyRepository.findByOuterRepayNo(repayRecord.getOuterRepayNo());
        Optional.ofNullable(repayApply).orElseThrow(() -> new BizException(ResultCode.REPAY_NOT_EXIST));

        repayApply.setApplyState(ProcessState.SUCCEED);
        offlineRepayApplyRepository.save(repayApply);

        List<RepayPlan> planList = repayPlanRepository.findByLoanIdOrderByPeriod(repayRecord.getLoanId());
        final int curPeriod = repayRecord.getPeriod();
        RepayPurpose purpose = repayRecord.getRepayPurpose();
        RepayPlan curPlan =
            planList.stream().filter(p -> p.getPeriod().compareTo(curPeriod) == 0).findFirst()
                .orElseThrow(() -> new BizException(ResultCode.REPAY_NOT_EXIST));
        curPlan.setCustRepayState(RepayState.REPAID);
        repayPlanRepository.save(curPlan);

        if (RepayPurpose.CLEAR == purpose) {
            planList.stream().filter(p -> p.getPeriod() > curPeriod).forEach(p -> {
                p.setActAmount(BigDecimal.ZERO);
                p.setActPrincipalAmt(BigDecimal.ZERO);
                p.setActInterestAmt(BigDecimal.ZERO);
                p.setActPenaltyAmt(BigDecimal.ZERO);
                p.setActGuaranteeAmt(BigDecimal.ZERO);
                p.setActBreachAmt(BigDecimal.ZERO);
                p.setActConsultFee(BigDecimal.ZERO);
                p.setActRepayTime(curPlan.getActRepayTime());
                p.setCustRepayState(RepayState.REPAID);
                repayPlanRepository.save(p);
            });
        }
    }

    @Autowired
    public void setRepayPlanRepository(RepayPlanRepository repayPlanRepository) {
        this.repayPlanRepository = repayPlanRepository;
    }

    @Autowired
    public void setLoanRepository(LoanRepository loanRepository) {
        this.loanRepository = loanRepository;
    }

    @Autowired
    public void setTrailService(TrialService trialService) {
        this.trialService = trialService;
    }

    @Autowired
    public void setChargeService(ChargeService chargeService) {
        this.chargeService = chargeService;
    }

    @Autowired
    public void setRecordRepository(CustomRepayRecordRepository recordRepository) {
        this.recordRepository = recordRepository;
    }

    @Autowired
    public void setBankRepayRecordRepository(BankRepayRecordRepository bankRepayRecordRepository) {
        this.bankRepayRecordRepository = bankRepayRecordRepository;
    }
}
