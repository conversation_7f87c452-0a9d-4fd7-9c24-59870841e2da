# Cash-Manage 项目管理功能设计方案

## 📋 项目概述

本文档详细描述了在Cash-Manage系统中新增项目管理功能的设计方案，以及为Flow和Capital系统提供项目配置查询接口的实现方案。

## 🎯 需求分析

### 业务需求
- **项目配置管理**: 在Cash-Manage系统中增加项目管理相关功能
- **配置共享**: Flow和Capital系统需要调用Manage的接口获取项目配置
- **统一管理**: 实现项目配置的统一管理和分发

### 技术需求
- **职责清晰**: Manage负责提供接口，Flow/Capital负责消费
- **架构一致**: 遵循现有的API包设计模式
- **易于维护**: 接口定义统一管理，避免重复定义

## 🏗️ 整体架构设计

### 模块架构图
```mermaid
graph TB
    subgraph "Cash-Manage System"
        A[cash-manage-api] --> B[cash-manage-system]
        B --> C[ProjectConfigController]
        C --> D[ProjectConfigService]
        D --> E[ProjectConfigRepository]
        E --> F[project_config表]
    end
    
    subgraph "Flow System"
        G[ProjectConfigClient] --> H[业务服务层]
        H --> I[本地缓存]
    end
    
    subgraph "Capital System"
        J[ProjectConfigClient] --> K[业务服务层]
        K --> L[本地缓存]
    end
    
    G -.->|Feign调用| C
    J -.->|Feign调用| C
    
    A -.->|依赖| G
    A -.->|依赖| J
```

### 职责划分
- **cash-manage-api**: 定义项目配置相关的API接口
- **cash-manage-system**: 实现项目配置的业务逻辑和数据管理
- **flow/capital**: 通过Feign客户端调用项目配置接口

## 📦 模块设计

### 1. cash-manage-api 模块

#### 模块结构
```
cash-manage-api/
├── pom.xml
└── src/main/java/com/jinghang/cash/api/
    ├── ProjectConfigApi.java          # 项目配置API接口
    ├── dto/                           # 数据传输对象
    │   ├── ProjectConfigDto.java      # 项目配置DTO
    │   ├── ProjectConfigDetailDto.java # 项目配置详情DTO
    │   └── ProjectConfigQueryDto.java  # 查询条件DTO
    └── enums/                         # 枚举定义
        ├── ProjectStatus.java         # 项目状态枚举
        └── ConfigType.java           # 配置类型枚举
```

#### 核心接口定义
```java
/**
 * 项目配置API接口
 */
public interface ProjectConfigApi {
    
    /**
     * 根据项目编码获取项目配置
     */
    @GetMapping("/config/project/{projectCode}")
    RestResult<ProjectConfigDto> getProjectConfig(@PathVariable String projectCode);
    
    /**
     * 根据流量渠道获取项目配置列表
     */
    @GetMapping("/config/project/list")
    RestResult<List<ProjectConfigDto>> getProjectConfigsByChannel(@RequestParam String flowChannel);
    
    /**
     * 批量获取项目配置
     */
    @PostMapping("/config/project/batch")
    RestResult<Map<String, ProjectConfigDto>> batchGetProjectConfigs(@RequestBody List<String> projectCodes);
    
    /**
     * 获取项目配置详情（包含配置项）
     */
    @GetMapping("/config/project/{projectCode}/detail")
    RestResult<ProjectConfigDetailDto> getProjectConfigDetail(@PathVariable String projectCode);
    
    /**
     * 根据业务类型和环境获取项目配置
     */
    @GetMapping("/config/project/query")
    RestResult<List<ProjectConfigDto>> queryProjectConfigs(@RequestParam String businessType, 
                                                          @RequestParam String envType);
}
```

#### POM配置
```xml
<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0">
    <parent>
        <groupId>com.jinghang.cash</groupId>
        <artifactId>loan-cash-manage</artifactId>
        <version>1.0</version>
    </parent>
    
    <artifactId>cash-manage-api</artifactId>
    <name>Cash-Manage API模块</name>
    <description>项目配置管理API接口定义</description>
    
    <dependencies>
        <!-- Spring Web -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
        </dependency>
        
        <!-- 通用工具 -->
        <dependency>
            <groupId>com.jinghang.common.util</groupId>
            <artifactId>common-util</artifactId>
        </dependency>
        
        <!-- 验证注解 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>
    </dependencies>
</project>
```

### 2. cash-manage-system 模块改造

#### 新增依赖
```xml
<!-- 在 cash-manage-system/pom.xml 中新增 -->
<dependency>
    <groupId>com.jinghang.cash</groupId>
    <artifactId>cash-manage-api</artifactId>
    <version>1.0</version>
</dependency>
```

#### 数据库表设计
```sql
-- 项目配置主表
CREATE TABLE project_config (
    id VARCHAR(32) PRIMARY KEY COMMENT '主键ID',
    project_code VARCHAR(50) NOT NULL UNIQUE COMMENT '项目编码',
    project_name VARCHAR(100) NOT NULL COMMENT '项目名称',
    project_desc VARCHAR(500) COMMENT '项目描述',
    status VARCHAR(20) NOT NULL DEFAULT 'ENABLE' COMMENT '项目状态(ENABLE/DISABLE)',
    flow_channel VARCHAR(50) COMMENT '流量渠道',
    business_type VARCHAR(50) COMMENT '业务类型',
    env_type VARCHAR(20) NOT NULL DEFAULT 'PROD' COMMENT '环境类型(DEV/TEST/PROD)',
    priority INT DEFAULT 0 COMMENT '优先级',
    effective_start_time DATETIME COMMENT '生效开始时间',
    effective_end_time DATETIME COMMENT '生效结束时间',
    remark VARCHAR(500) COMMENT '备注',
    revision VARCHAR(32) COMMENT '版本号',
    created_by VARCHAR(50) COMMENT '创建人',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(50) COMMENT '更新人',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_project_code (project_code),
    INDEX idx_flow_channel (flow_channel),
    INDEX idx_business_type (business_type),
    INDEX idx_status (status)
) COMMENT='项目配置主表';

-- 项目配置详情表
CREATE TABLE project_config_detail (
    id VARCHAR(32) PRIMARY KEY COMMENT '主键ID',
    project_id VARCHAR(32) NOT NULL COMMENT '项目ID',
    config_key VARCHAR(100) NOT NULL COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    config_type VARCHAR(50) NOT NULL COMMENT '配置类型',
    config_desc VARCHAR(200) COMMENT '配置描述',
    is_encrypted TINYINT(1) DEFAULT 0 COMMENT '是否加密',
    sort_order INT DEFAULT 0 COMMENT '排序',
    created_by VARCHAR(50) COMMENT '创建人',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(50) COMMENT '更新人',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (project_id) REFERENCES project_config(id),
    UNIQUE KEY uk_project_config (project_id, config_key),
    INDEX idx_config_type (config_type)
) COMMENT='项目配置详情表';
```

#### 控制器实现
```java
@RestController
@RequestMapping("/api")
@Slf4j
public class ProjectConfigApiController implements ProjectConfigApi {
    
    @Autowired
    private ProjectConfigService projectConfigService;
    
    @Override
    public RestResult<ProjectConfigDto> getProjectConfig(String projectCode) {
        log.info("获取项目配置, projectCode: {}", projectCode);
        try {
            ProjectConfigDto config = projectConfigService.getByProjectCode(projectCode);
            return RestResult.success(config);
        } catch (Exception e) {
            log.error("获取项目配置失败, projectCode: {}", projectCode, e);
            return RestResult.fail("获取项目配置失败: " + e.getMessage());
        }
    }
    
    @Override
    public RestResult<List<ProjectConfigDto>> getProjectConfigsByChannel(String flowChannel) {
        log.info("根据流量渠道获取项目配置, flowChannel: {}", flowChannel);
        try {
            List<ProjectConfigDto> configs = projectConfigService.getByFlowChannel(flowChannel);
            return RestResult.success(configs);
        } catch (Exception e) {
            log.error("根据流量渠道获取项目配置失败, flowChannel: {}", flowChannel, e);
            return RestResult.fail("获取项目配置失败: " + e.getMessage());
        }
    }
    
    @Override
    public RestResult<Map<String, ProjectConfigDto>> batchGetProjectConfigs(List<String> projectCodes) {
        log.info("批量获取项目配置, projectCodes: {}", projectCodes);
        try {
            Map<String, ProjectConfigDto> configMap = projectConfigService.batchGetByProjectCodes(projectCodes);
            return RestResult.success(configMap);
        } catch (Exception e) {
            log.error("批量获取项目配置失败, projectCodes: {}", projectCodes, e);
            return RestResult.fail("批量获取项目配置失败: " + e.getMessage());
        }
    }
    
    // 其他方法实现...
}
```

### 3. Flow系统集成

#### 依赖配置
```xml
<!-- 在 flow 模块的 pom.xml 中新增 -->
<dependency>
    <groupId>com.jinghang.cash</groupId>
    <artifactId>cash-manage-api</artifactId>
    <version>1.0</version>
</dependency>
```

#### Feign客户端
```java
@FeignClient(name = "cash-manage", contextId = "projectConfig", path = "/api")
public interface ProjectConfigClient extends ProjectConfigApi {
}
```

#### 服务层使用
```java
@Service
@Slf4j
public class FlowProjectConfigService {
    
    @Autowired
    private ProjectConfigClient projectConfigClient;
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    private static final String CACHE_PREFIX = "project:config:";
    private static final long CACHE_EXPIRE = 3600; // 1小时
    
    /**
     * 获取项目配置（带缓存）
     */
    public ProjectConfigDto getProjectConfig(String projectCode) {
        String cacheKey = CACHE_PREFIX + projectCode;
        
        // 先从缓存获取
        ProjectConfigDto cached = (ProjectConfigDto) redisTemplate.opsForValue().get(cacheKey);
        if (cached != null) {
            return cached;
        }
        
        // 缓存未命中，调用远程接口
        try {
            RestResult<ProjectConfigDto> result = projectConfigClient.getProjectConfig(projectCode);
            if (result.isSuccess()) {
                ProjectConfigDto config = result.getData();
                // 写入缓存
                redisTemplate.opsForValue().set(cacheKey, config, CACHE_EXPIRE, TimeUnit.SECONDS);
                return config;
            } else {
                log.error("获取项目配置失败: {}", result.getMsg());
                throw new BizException("获取项目配置失败");
            }
        } catch (Exception e) {
            log.error("调用项目配置接口异常, projectCode: {}", projectCode, e);
            throw new BizException("获取项目配置异常");
        }
    }
}
```

### 4. Capital系统集成

#### 依赖和客户端配置
```xml
<!-- 在 capital 模块的 pom.xml 中新增 -->
<dependency>
    <groupId>com.jinghang.cash</groupId>
    <artifactId>cash-manage-api</artifactId>
    <version>1.0</version>
</dependency>
```

```java
@FeignClient(name = "cash-manage", contextId = "projectConfig", path = "/api")
public interface ProjectConfigClient extends ProjectConfigApi {
}
```

#### 使用示例
```java
@Service
@Slf4j
public class CapitalProjectConfigService {
    
    @Autowired
    private ProjectConfigClient projectConfigClient;
    
    /**
     * 根据业务类型获取项目配置
     */
    public List<ProjectConfigDto> getProjectConfigsByBusinessType(String businessType, String envType) {
        try {
            RestResult<List<ProjectConfigDto>> result = 
                projectConfigClient.queryProjectConfigs(businessType, envType);
            
            if (result.isSuccess()) {
                return result.getData();
            } else {
                log.error("获取项目配置失败: {}", result.getMsg());
                return Collections.emptyList();
            }
        } catch (Exception e) {
            log.error("调用项目配置接口异常, businessType: {}, envType: {}", businessType, envType, e);
            return Collections.emptyList();
        }
    }
}
```

## 🔧 配置管理

### 服务发现配置
```yaml
# cash-manage 服务配置
spring:
  application:
    name: cash-manage
  cloud:
    nacos:
      discovery:
        server-addr: ${nacos.server.addr}

# flow/capital 服务配置
feign:
  client:
    config:
      cash-manage:
        connect-timeout: 5000
        read-timeout: 10000
        logger-level: basic
  hystrix:
    enabled: true

hystrix:
  command:
    default:
      execution:
        isolation:
          thread:
            timeoutInMilliseconds: 10000
```

### 缓存配置
```java
@Configuration
@EnableCaching
public class ProjectConfigCacheConfig {
    
    @Bean
    public CacheManager projectConfigCacheManager() {
        RedisCacheManager.Builder builder = RedisCacheManager
            .RedisCacheManagerBuilder
            .fromConnectionFactory(redisConnectionFactory())
            .cacheDefaults(cacheConfiguration());
        
        return builder.build();
    }
    
    private RedisCacheConfiguration cacheConfiguration() {
        return RedisCacheConfiguration.defaultCacheConfig()
            .entryTtl(Duration.ofHours(1))
            .serializeKeysWith(RedisSerializationContext.SerializationPair
                .fromSerializer(new StringRedisSerializer()))
            .serializeValuesWith(RedisSerializationContext.SerializationPair
                .fromSerializer(new GenericJackson2JsonRedisSerializer()));
    }
}
```

## 📋 实施计划

### 阶段一：基础架构搭建（1-2天）
1. 创建 cash-manage-api 模块
2. 定义核心API接口和DTO
3. 设计数据库表结构

### 阶段二：Manage系统实现（2-3天）
1. 实现项目配置的CRUD功能
2. 实现API接口控制器
3. 添加缓存机制

### 阶段三：Flow/Capital集成（1-2天）
1. 在Flow和Capital中添加Feign客户端
2. 实现配置调用服务
3. 添加本地缓存和容错机制

### 阶段四：测试和优化（1-2天）
1. 单元测试和集成测试
2. 性能测试和优化
3. 文档完善

## 🔍 关键注意事项

### 1. 版本管理
- API包版本需要统一管理
- 接口变更时注意向后兼容性
- 建议使用语义化版本控制

### 2. 性能优化
- 实现多级缓存（本地缓存 + Redis缓存）
- 批量接口减少网络调用
- 异步刷新缓存机制

### 3. 容错处理
- 添加熔断降级机制
- 实现优雅的失败处理
- 提供默认配置兜底

### 4. 监控告警
- 添加接口调用监控
- 配置异常告警
- 性能指标监控

## 📊 预期收益

### 技术收益
- ✅ **架构清晰**: 职责分离，易于维护
- ✅ **代码复用**: 避免重复定义接口
- ✅ **类型安全**: 编译期检查，减少错误
- ✅ **统一管理**: 配置集中管理，便于维护

### 业务收益
- ✅ **配置统一**: 项目配置统一管理
- ✅ **效率提升**: 减少配置维护成本
- ✅ **灵活性**: 支持动态配置变更
- ✅ **可扩展**: 易于扩展新的配置类型

---

*文档版本: v1.0*  
*创建时间: 2025-01-19*  
*维护团队: 金航科技开发团队*
