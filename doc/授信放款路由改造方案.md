# 授信放款路由改造方案

## 📋 改造目标

在保留现有路由逻辑的基础上，新增一套直连资方授信放款逻辑，根据项目要素配置(`ProjectElements.capitalRoute`)来判断走直连还是路由模式。

## 🏗️ 整体架构设计

### 当前架构分析
- **路由模式**: 通过`OrderRouterService`进行多资方路由，失败后自动切换下一资方
- **直连模式**: 直接调用指定资方，无路由切换逻辑
- **判断依据**: `ProjectElements.capitalRoute`字段 (`DIRECT`-直连, `ROUTE`-路由)

### 改造策略
1. **保留原有路由逻辑**: 确保现有功能不受影响
2. **新增直连逻辑**: 基于项目要素配置的直连流程
3. **统一入口**: 在现有服务中增加路由模式判断
4. **保留校验**: 直连模式下保留原路由中的各种业务校验

---

## 🎯 一、授信改造方案

### 1.1 核心改造点

#### 1.1.1 CreditService改造
在`CreditService.apply()`方法中增加路由模式判断：

```java
public void apply(Order order, String routerId) {
    // 获取项目信息
    ProjectInfoVO projectInfo = projectInfoService.queryProjectInfo(order.getProjectCode());

    if (projectInfo != null && projectInfo.getElements() != null) {
        String capitalRoute = projectInfo.getElements().getCapitalRoute();

        if (CapitalRoute.DIRECT.getCode().equals(capitalRoute)) {
            // 直连模式授信
            applyDirect(order, routerId);
        } else {
            // 路由模式授信（保留原逻辑）
            applyRoute(order, routerId);
        }
    } else {
        // 默认走路由模式
        applyRoute(order, routerId);
    }
}
```

#### 1.1.2 新增直连授信方法
```java
private void applyDirect(Order order, String routerId) {
    // 1. 初始化授信记录
    Credit credit = initCredit(order, routerId);

    // 2. 保留原有校验逻辑
    validateCreditApply(order, credit);

    // 3. 协议签署
    agreementService.applySign(credit.getOrderId(), LoanStage.CREDIT,
                              credit.getFlowChannel(), credit.getBankChannel());

    // 4. 直接提交到指定资方（无路由）
    mqService.submitDirectCreditApply(credit.getId());
}
```

#### 1.1.3 保留原有路由方法
```java
private void applyRoute(Order order, String routerId) {
    // 保持原有逻辑不变
    Credit credit = initCredit(order, routerId);
    agreementService.applySign(credit.getOrderId(), LoanStage.CREDIT,
                              credit.getFlowChannel(), credit.getBankChannel());
    mqService.submitCreditApply(credit.getId());
}
```

### 1.2 校验逻辑保留

#### 1.2.1 业务校验方法
```java
private void validateCreditApply(Order order, Credit credit) {
    // 1. 用户基本信息校验
    validateUserInfo(order);

    // 2. 授信额度校验
    validateCreditLimit(order, credit);

    // 3. 黑暗期校验
    validateDarkHours(order, credit);

    // 4. 风控规则校验
    validateRiskRules(order, credit);

    // 5. 资方配置校验
    validateCapitalConfig(order, credit);
}
```

#### 1.2.2 直连资方确定逻辑
```java
private BankChannel getDirectBankChannel(Order order) {
    // 根据项目配置或订单信息确定直连资方
    ProjectInfoVO projectInfo = projectInfoService.queryProjectInfo(order.getProjectCode());

    if (projectInfo != null) {
        // 可以从项目配置中获取指定的资方
        String capitalChannel = projectInfo.getCapitalChannel();
        return BankChannel.valueOf(capitalChannel);
    }

    // 或者从订单中获取预设的资方
    return order.getBankChannel();
}
```

### 1.3 MQ消息处理改造

#### 1.3.1 新增直连授信MQ方法
```java
// MqService中新增
public void submitDirectCreditApply(String creditId) {
    CreditApplyMessage message = new CreditApplyMessage();
    message.setCreditId(creditId);
    message.setDirect(true); // 标记为直连模式

    rabbitTemplate.convertAndSend(DIRECT_CREDIT_APPLY_QUEUE, message);
}
```

#### 1.3.2 授信处理监听器改造
```java
@RabbitListener(queues = DIRECT_CREDIT_APPLY_QUEUE)
public void handleDirectCreditApply(CreditApplyMessage message) {
    String creditId = message.getCreditId();

    try {
        // 直连授信处理
        creditService.bankCreditDirect(creditId);
    } catch (Exception e) {
        logger.error("直连授信处理失败: {}", creditId, e);
        // 直连失败处理（不进行路由重试）
        creditService.handleDirectCreditFailed(creditId, e.getMessage());
    }
}
```

---

## 🎯 二、放款改造方案

### 2.1 核心改造点

#### 2.1.1 LoanService改造
在放款申请入口增加路由模式判断：

```java
public Loan apply(String creditId) {
    Credit credit = creditRepository.findById(creditId)
        .orElseThrow(() -> new BizException(ResultCode.CREDIT_NOT_EXIST));

    // 获取项目信息判断路由模式
    ProjectInfoVO projectInfo = projectInfoService.queryProjectInfo(credit.getProjectCode());

    if (projectInfo != null && projectInfo.getElements() != null) {
        String capitalRoute = projectInfo.getElements().getCapitalRoute();

        if (CapitalRoute.DIRECT.getCode().equals(capitalRoute)) {
            // 直连模式放款
            return applyDirect(credit);
        } else {
            // 路由模式放款（保留原逻辑）
            return applyRoute(credit);
        }
    } else {
        // 默认走路由模式
        return applyRoute(credit);
    }
}
```

#### 2.1.2 新增直连放款方法
```java
private Loan applyDirect(Credit credit) {
    // 1. 保留原有校验逻辑
    validateLoanApply(credit);

    // 2. 初始化放款记录
    Loan loan = initLoan(credit);
    loan.setDirect(true); // 标记为直连模式

    // 3. 直接提交到指定资方
    mqService.submitDirectLoanApply(loan.getId());

    return loan;
}
```

#### 2.1.3 保留原有路由方法
```java
private Loan applyRoute(Credit credit) {
    // 保持原有逻辑不变
    validateLoanApply(credit);
    Loan loan = initLoan(credit);
    mqService.submitLoanApply(loan.getId());
    return loan;
}
```

### 2.2 校验逻辑保留

#### 2.2.1 放款校验方法
```java
private void validateLoanApply(Credit credit) {
    // 1. 授信状态校验
    validateCreditStatus(credit);

    // 2. 放款额度校验
    validateLoanLimit(credit);

    // 3. 放款黑暗期校验
    validateLoanDarkHours(credit);

    // 4. 银行卡校验
    validateBankCard(credit);

    // 5. 资方配置校验
    validateCapitalConfigForLoan(credit);
}
```

#### 2.2.2 直连放款失败处理
```java
public void handleDirectLoanFailed(String loanId, String failReason) {
    Loan loan = loanRepository.findById(loanId)
        .orElseThrow(() -> new BizException(ResultCode.LOAN_NOT_EXIST));

    // 直连模式失败，不进行路由重试
    loan.setLoanState(ProcessState.FAILED);
    loan.setFailReason(failReason);
    loan.setUpdatedTime(LocalDateTime.now());

    loanRepository.save(loan);

    // 发送失败通知
    eventPublisher.publishEvent(new LoanFailedEvent(loan));
}
```

### 2.3 放款路由改造

#### 2.3.1 LoanCommonService改造
在放款限制检查中增加直连模式判断：

```java
public LoanSuspendFlag limitSuspend(Loan loan) {
    // 获取项目信息
    ProjectInfoVO projectInfo = projectInfoService.queryProjectInfo(loan.getProjectCode());
    boolean isDirect = projectInfo != null && projectInfo.getElements() != null
        && CapitalRoute.DIRECT.getCode().equals(projectInfo.getElements().getCapitalRoute());

    if (isDirect) {
        // 直连模式：不进行自动路由，直接返回暂停
        return handleDirectLoanSuspend(loan);
    } else {
        // 路由模式：保持原有逻辑
        return handleRouteLoanSuspend(loan);
    }
}
```

---

## 🔧 三、技术实现细节

### 3.1 数据库改造

#### 3.1.1 Credit表增加字段
```sql
ALTER TABLE credit ADD COLUMN is_direct TINYINT(1) DEFAULT 0 COMMENT '是否直连模式 0-路由 1-直连';
```

#### 3.1.2 Loan表增加字段
```sql
ALTER TABLE loan ADD COLUMN is_direct TINYINT(1) DEFAULT 0 COMMENT '是否直连模式 0-路由 1-直连';
```

### 3.2 配置管理

#### 3.2.1 项目要素配置
确保`ProjectElements.capitalRoute`字段正确配置：
- `DIRECT`: 直连模式
- `ROUTE`: 路由模式

#### 3.2.2 MQ队列配置
```yaml
# 新增直连相关队列
rabbitmq:
  queues:
    direct-credit-apply: direct.credit.apply
    direct-loan-apply: direct.loan.apply
```

### 3.3 监控和日志

#### 3.3.1 日志增强
```java
// 在关键节点增加路由模式标识
logger.info("授信申请开始 - 模式: {}, creditId: {}",
           isDirect ? "直连" : "路由", creditId);
```

#### 3.3.2 监控指标
- 直连模式成功率
- 路由模式成功率
- 模式切换统计

---

## 📊 四、测试方案

### 4.1 单元测试
- 路由模式判断逻辑测试
- 直连授信流程测试
- 直连放款流程测试
- 校验逻辑保留测试

### 4.2 集成测试
- 端到端直连流程测试
- 路由模式兼容性测试
- 异常场景处理测试

### 4.3 性能测试
- 直连模式性能对比
- 路由模式性能影响评估

---

## 🚀 五、部署计划

### 5.1 灰度发布
1. 先在测试环境验证
2. 生产环境小流量灰度
3. 逐步扩大直连流量

### 5.2 回滚方案
- 配置回滚：修改`capitalRoute`为`ROUTE`
- 代码回滚：保留原有路由逻辑作为兜底

---

## ⚠️ 注意事项

1. **向后兼容**: 确保现有路由逻辑完全不受影响
2. **校验保留**: 直连模式必须保留所有原有校验逻辑
3. **异常处理**: 直连失败不进行自动路由重试
4. **监控告警**: 增加直连模式相关监控指标
5. **配置管理**: 项目要素配置的准确性至关重要
